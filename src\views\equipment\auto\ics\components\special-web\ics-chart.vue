<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import realTimeChart from "./real-time-chart.vue";
import dayjs from "dayjs";

const props = defineProps({
  equipId: {
    require: true,
    type: String
  },
  mark: {
    require: true,
    type: String
  },
  markNum: {
    require: true,
    type: Number
  },
  pageWidth: {
    require: true,
    type: Number
  },
  pageHeight: {
    require: true,
    type: Number
  }
});

const selectedDates = ref();

const subChartOpen = ref(false);

const edChartRef = ref(null);
//const gtChartRef = ref(null);
const exChartRef = ref(null);
const eyChartRef = ref(null);

onBeforeMount(() => {
  console.log(props.equipId);

  selectedDates.value = [dayjs().format("YYYY-MM-DD")];
});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const callReload = () => {
  edChartRef.value.reloadData(selectedDates.value);
  if (subChartOpen.value) {
    //gtChartRef.value.reloadData(selectedDates.value);
    exChartRef.value.reloadData(selectedDates.value);
    eyChartRef.value.reloadData(selectedDates.value);
  }
};

defineExpose({
  subChartOpen,
  callReload
});

// Note: Chart宽度计算公式勿乱动, 数值是通过解二元一次方程得出, 适配1080p与SAE办公显示器分辨率
</script>

<template>
  <el-collapse-item :name="props.mark + props.markNum">
    <template #title>
      <el-row align="middle">
        <el-col align="middle">
          <el-descriptions border size="large" :column="1" direction="vertical">
            <el-descriptions-item>
              <template v-slot:label>
                <el-row align="middle">
                  <el-col :span="8">
                    <el-date-picker
                      v-model="selectedDates"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledDate"
                      type="dates"
                      placeholder="Pick one or more dates"
                      :clearable="false"
                      @change="callReload"
                    />
                  </el-col>
                  <el-col :span="8" align="middle">
                    <span>{{ "ED" + props.markNum }}</span>
                  </el-col>
                  <el-col :span="8" />
                </el-row>
              </template>
              <el-row align="middle">
                <el-col align="middle">
                  <realTimeChart
                    :equip-id="props.equipId"
                    :chart-id="props.mark + 'ED' + props.markNum"
                    :chart-param="'ED' + props.markNum"
                    :c-width="
                      ((props.pageWidth / props.pageHeight + 11.71) / 15) *
                        props.pageWidth +
                      'px'
                    "
                    :c-height="'400px'"
                    :selected-dates="selectedDates"
                    ref="edChartRef"
                  />
                </el-col>
              </el-row>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </template>
    <el-descriptions
      v-if="subChartOpen"
      border
      size="large"
      :column="2"
      direction="vertical"
    >
      <!-- <el-descriptions-item
        :label="'Glue Time' + props.markNum"
        label-align="center"
      >
        <el-row align="middle" style="margin: 0px; padding: 0px">
          <el-col align="middle" style="margin: 0px; padding: 0px">
            <realTimeChart
              :equip-id="props.equipId"
              :chart-id="props.mark + 'GLUE' + props.markNum"
              :chart-param="'GT' + props.markNum"
              :c-width="
                ((props.pageWidth / props.pageHeight + 11.71) / 15) *
                  props.pageWidth *
                  0.31 +
                'px'
              "
              :c-height="'300px'"
              :selected-dates="selectedDates"
              ref="gtChartRef"
            /> </el-col
        ></el-row>
      </el-descriptions-item> -->
      <el-descriptions-item
        align="center"
        :label="'EX' + props.markNum"
        label-align="center"
      >
        <el-row align="middle">
          <el-col align="middle">
            <realTimeChart
              :equip-id="props.equipId"
              :chart-id="props.mark + 'EX' + props.markNum"
              :chart-param="'EX' + props.markNum"
              :c-width="
                ((props.pageWidth / props.pageHeight + 11.71) / 15) *
                  props.pageWidth *
                  0.5 +
                'px'
              "
              :c-height="'300px'"
              :selected-dates="selectedDates"
              ref="exChartRef"
            />
          </el-col>
        </el-row>
      </el-descriptions-item>
      <el-descriptions-item
        align="center"
        :label="'EY' + props.markNum"
        label-align="center"
      >
        <el-row align="middle">
          <el-col align="middle">
            <realTimeChart
              :equip-id="props.equipId"
              :chart-id="props.mark + 'EY' + props.markNum"
              :chart-param="'EY' + props.markNum"
              :c-width="
                ((props.pageWidth / props.pageHeight + 11.71) / 15) *
                  props.pageWidth *
                  0.5 +
                'px'
              "
              :c-height="'300px'"
              :selected-dates="selectedDates"
              ref="eyChartRef"
            />
          </el-col>
        </el-row>
      </el-descriptions-item>
    </el-descriptions>
  </el-collapse-item>
</template>

<style scoped>
:deep(.el-collapse-item__header) {
  height: 500px;
}
</style>
