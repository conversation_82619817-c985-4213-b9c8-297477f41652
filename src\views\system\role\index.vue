<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { roleAPI } from "/@/api/system/role";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";

import roleEdit from "./components/role-edit.vue";

defineOptions({
  name: "Role Manage"
});

const pageData = reactive({
  loading: false,
  selection: [],
  dataList: [],
  searchInfo: {
    roleName: "",
    role: "",
    type: -1,
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  }
});

const editting = ref(false);

const edittingObj = ref();

const lastSortNum = ref(0);

const getPage = () => {
  pageData.loading = true;
  roleAPI
    .page(pageData.searchInfo)
    .then((res: any) => {
      if (res !== "fail") {
        pageData.dataList = res.records;
        pageData.searchInfo.total = Number(res.total);
        lastSortNum.value = Number(res.maxLimit);
      }
    })
    .finally(() => (pageData.loading = false));
};

onMounted(() => {
  getPage();
});

const searchForm = reactive({
  name: "",
  role: "",
  type: null
});

const onSearch = () => {
  pageData.searchInfo.roleName = searchForm.name;
  pageData.searchInfo.role = searchForm.role;
  pageData.searchInfo.type = searchForm.type;
  pageData.searchInfo.pageNum = 1;
  getPage();
};

const resetSearchForm = () => {
  searchForm.name = "";
  searchForm.role = "";
  searchForm.type = null;
};

const handleEdit = row => {
  edittingObj.value = row;
  if (row !== null) {
    edittingObj.value.type = row.id < 100 ? 0 : 1;
  }
  editting.value = true;
};

const handleDel = row => {
  ElMessageBox({
    title: transformI18n("dialogue.title-warning"),
    message: transformI18n("dialogue.msg-confirm-del"),
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      roleAPI.deleteById(row.id).then((res: any) => {
        if (res !== "fail") {
          getPage();
        }
      });
    })
    .catch(_err => {});
};

const sizeChange = pageSize => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = pageNum => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const onCloseRoleEdit = () => {
  editting.value = false;
  getPage();
};
</script>

<template>
  <div>
    <el-card class="query-card">
      <el-form :inline="true" :model="searchForm">
        <el-form-item
          :label="transformI18n('role.edit-form-roleName') + ': '"
          prop="name"
        >
          <el-input
            v-model="searchForm.name"
            :placeholder="transformI18n('role.query-roleName')"
            clearable
            class="!w-[150px]"
          />
        </el-form-item>
        <el-form-item
          :label="transformI18n('role.edit-form-role') + ': '"
          prop="code"
        >
          <el-input
            v-model="searchForm.role"
            :placeholder="transformI18n('role.query-role')"
            clearable
            class="!w-[100px]"
          />
        </el-form-item>
        <el-form-item
          :label="transformI18n('role.edit-form-type') + ': '"
          prop="status"
        >
          <el-select
            v-model="searchForm.type"
            :placeholder="transformI18n('role.query-type')"
            clearable
            class="!w-[150px]"
          >
            <el-option
              :label="transformI18n('role.query-select-system')"
              value="0"
            />
            <el-option
              :label="transformI18n('role.query-select-custom')"
              value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            :loading="pageData.loading"
            @click="onSearch"
          >
            {{ transformI18n("query.search") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('refresh')"
            :loading="pageData.loading"
            @click="resetSearchForm"
          >
            {{ transformI18n("query.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <el-row align="middle">
        <el-col :span="20" align="left">
          <span style="font-weight: bold; font-size: 20px">{{
            transformI18n("role.title")
          }}</span>
        </el-col>
        <el-col :span="4" align="center">
          <el-button
            type="primary"
            :icon="useRenderIcon('document-add')"
            @click="handleEdit(null)"
            size="large"
            v-auth="['role:create']"
            >{{ transformI18n("role.edit-form-add-button") }}</el-button
          >
        </el-col>
      </el-row>
      <el-divider />
      <el-table
        v-loading="pageData.loading"
        :data="pageData.dataList"
        style="width: 100%"
        row-key="id"
        border
      >
        <el-table-column
          prop="sort"
          :label="transformI18n('role.edit-form-sort')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="roleName"
          :label="transformI18n('role.edit-form-roleName')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="role"
          :label="transformI18n('role.edit-form-role')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="transformI18n('role.edit-form-type')"
          resizable
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <el-tag :type="scope.row.id < 100 ? 'danger' : ''">{{
              scope.row.id < 100
                ? transformI18n("role.query-select-system")
                : transformI18n("role.query-select-custom")
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createdAt"
          :label="transformI18n('role.edit-form-createdAt')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          fixed="right"
          width="150"
          :label="transformI18n('role.list-operations')"
          :show-overflow-tooltip="true"
          v-auth="['role:update', 'role:delete']"
        >
          <template v-slot="scope">
            <el-button
              size="small"
              :link="true"
              :icon="useRenderIcon('edit')"
              v-auth="['role:update']"
              @click="handleEdit(scope.row)"
              type="primary"
            >
              <span>{{ transformI18n("buttons.hsedit") }}</span>
            </el-button>
            <el-button
              size="small"
              :link="true"
              :icon="useRenderIcon('delete')"
              v-auth="['role:delete']"
              @click="handleDel(scope.row)"
              type="danger"
              :disabled="scope.row.id < 100 ? true : false"
            >
              <span>{{ transformI18n("buttons.hsdelete") }}</span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
    <role-edit
      v-if="editting"
      :closeCallBack="onCloseRoleEdit"
      :entity="edittingObj"
      :lastSortNum="lastSortNum"
    />
  </div>
</template>

<style scoped>
.el-card {
  margin: 0px;
  padding: 0px;
  align-items: center;
  justify-content: center;
  width: 100% !important;
}

.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.el-form {
  justify-content: center;
  align-items: center;
  height: 100%;
}

.el-pagination {
  margin-top: 10px;
}
</style>
