<script setup lang="ts">
import { onMounted, reactive, watch, ref } from "vue";
import * as echarts from "echarts";
import { ocrIcsTxtAPI } from "/@/api/equipment/ocrIcsTxt";
import {
  paramList,
  FileVO,
  OcrIcsChartVO1,
  getFileListParam
} from "/@/api/model/equipment/ocr_ics_txt_model";
import ViewImage from "./ViewImage.vue";
import { errorMessage } from "/@/utils/message";

type EChartsOption = echarts.EChartsOption;

let yAxisMin = 0;
let yAxisMax = 0;

const init = (xLine: Array<string>, yLine: Array<object>) => {
  let myChart = echarts.init(document.getElementById("main1"));
  rightClickMenu.chart = myChart;
  let option: EChartsOption;

  option = {
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {},
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: xLine
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        min: yAxisMin == 0 ? null : yAxisMin,
        max: yAxisMax == 0 ? null : yAxisMax
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 10,
        borderColor: "rgba(0,0,0,0)",
        textStyle: {
          color: "#05D5FF"
        }
      }
    ],
    series: yLine
  };
  option && myChart.setOption(option);
};

const pageData = reactive<{
  searchTestInfo: paramList;
}>({
  searchTestInfo: {
    testTime: null,
    tester: null,
    position: null,
    paramType: "ED1",
    groupId: null
  }
});

const rightClickMenu = reactive<{
  visible: boolean;
  top: number;
  left: number;
  chart: any;
  time: string;
  fileVoArr: FileVO[];
}>({
  visible: false,
  top: 0,
  left: 0,
  chart: null,
  time: "",
  fileVoArr: []
});

const viewimage = ref();

defineOptions({
  name: "icsTxtStatisticLine"
});

const props = defineProps({
  txt: {
    type: Object as PropType<paramList>,
    required: true
  }
});

onMounted(() => {
  pageData.searchTestInfo = props.txt;
  getData();
});

const getData = () => {
  if (
    pageData.searchTestInfo == null ||
    pageData.searchTestInfo.testTime == null ||
    pageData.searchTestInfo.tester == null
  ) {
    return;
  }
  ocrIcsTxtAPI
    .getChartData1(pageData.searchTestInfo)
    .then((res: OcrIcsChartVO1 | string) => {
      if (res !== "fail" && res !== null) {
        const ocrIcsTxtVO = res as OcrIcsChartVO1;
        let xAxisData = ocrIcsTxtVO.xLine;
        let seriesData = [];

        const mean =
          ocrIcsTxtVO.oneLine.reduce((sum, x) => sum + parseFloat(x), 0) /
          ocrIcsTxtVO.oneLine.length;

        const variance =
          ocrIcsTxtVO.oneLine
            .map(x => (x - mean) ** 2)
            .reduce((sum, x) => sum + x, 0) /
          (ocrIcsTxtVO.oneLine.length - 1);
        const standardDeviation = Math.sqrt(variance);
        const UCL = mean + 3 * standardDeviation;
        const LCL = mean - 3 * standardDeviation;

        let oneCopy = ocrIcsTxtVO.oneLine.slice();
        oneCopy.push(parseFloat(UCL.toFixed(2)));
        oneCopy.push(parseFloat(LCL.toFixed(2)));

        let markLineData: any = {
          symbol: ["none", "none"],
          data: [
            {
              name: "Mean",
              type: "average",
              lineStyle: {
                type: "dashed",
                width: 2
              },
              label: {
                fontSize: 12,
                fontWeight: "bolder",
                color: "black",
                position: "middle",
                formatter: "{b}={c}"
              },
              zlevel: 5
            },
            {
              name: "UCL",
              yAxis: UCL,
              lineStyle: {
                color: "orange",
                type: "solid",
                width: 2
              },
              label: {
                formatter: "UCL",
                fontSize: 12,
                fontWeight: "bolder",
                color: "orange"
              },
              zlevel: 5
            },
            {
              name: "LCL",
              yAxis: LCL,
              lineStyle: {
                color: "orange",
                type: "solid",
                width: 2
              },
              label: {
                formatter: "LCL",
                fontSize: 12,
                fontWeight: "bolder",
                color: "orange"
              },
              zlevel: 5
            }
          ]
        };
        let spec = ocrIcsTxtVO.spec;
        if (spec !== undefined) {
          let uslVal = spec["USL"];
          // oneCopy.push(parseInt(uslVal));
          let _usl = {
            name: "USL",
            yAxis: parseInt(uslVal),
            lineStyle: {
              color: "red",
              type: "solid",
              width: 2
            },
            label: {
              formatter: "USL",
              fontSize: 12,
              fontWeight: "bolder",
              color: "red"
            },
            zlevel: 5
          };
          // markLineData.data.push(usl);
          let lslVal = spec["LSL"];
          // oneCopy.push(parseInt(lslVal));
          let _lsl = {
            name: "LSL",
            yAxis: parseInt(lslVal),
            lineStyle: {
              color: "red",
              type: "solid",
              width: 2
            },
            label: {
              formatter: "LSL",
              fontSize: 12,
              fontWeight: "bolder",
              color: "red"
            },
            zlevel: 5
          };
          // markLineData.data.push(lsl);
        }
        oneCopy.sort((a, b) => {
          return a < b ? -1 : a > b ? 1 : 0;
        });
        yAxisMin = oneCopy[0];
        yAxisMax = oneCopy[oneCopy.length - 1];

        var svoOne: any = {
          name: "The mean value of measurement per tray",
          type: "line",
          stack: "oneLine",
          yAxisIndex: 0,
          emphasis: {
            focus: "series"
          },
          markPoint: {
            data: [
              { type: "max", name: "Max" },
              { type: "min", name: "Min" }
            ]
          },
          symbol: "circle",
          symbolSize: 5,
          markLine: markLineData,
          itemStyle: {
            color: function (params) {
              // let uslVal = null;
              // let lslVal = null;
              // if (spec === undefined) {
              //   return "blue";
              // }
              // uslVal = spec["USL"];
              // lslVal = spec["LSL"];
              // if (
              //   ocrIcsTxtVO.oneLine[params.dataIndex] > parseInt(uslVal) ||
              //   ocrIcsTxtVO.oneLine[params.dataIndex] < parseInt(lslVal)
              // ) {
              //   return "red";
              // }
              if (
                ocrIcsTxtVO.oneLine[params.dataIndex] >
                  parseFloat(UCL.toFixed(2)) ||
                ocrIcsTxtVO.oneLine[params.dataIndex] <
                  parseFloat(LCL.toFixed(2))
              ) {
                return "orange";
              } else {
                return "blue";
              }
            }
          },
          data: ocrIcsTxtVO.oneLine,
          zlevel: 3
        };

        var _svoFive: any = {
          name: "Five tray",
          type: "line",
          stack: "fiveLine",
          yAxisIndex: 0,
          emphasis: {
            focus: "series"
          },
          markPoint: {
            data: [
              { type: "max", name: "Max" },
              { type: "min", name: "Min" }
            ]
          },
          markLine: null,
          data: ocrIcsTxtVO.fiveLine,
          zlevel: 2
        };

        var _svoTen: any = {
          name: "Ten tray",
          type: "line",
          stack: "tenLine",
          yAxisIndex: 0,
          emphasis: {
            focus: "series"
          },
          markPoint: {
            data: [
              { type: "max", name: "Max" },
              { type: "min", name: "Min" }
            ]
          },
          markLine: null,
          data: ocrIcsTxtVO.tenLine,
          zlevel: 1
        };

        seriesData.push(svoOne);
        // seriesData.push(svoFive);
        // seriesData.push(svoTen);

        init(xAxisData, seriesData);
      } else {
        init([], []);
      }
    });
};

// 从鼠标事件监听右键菜单可见值更新
watch(
  () => rightClickMenu.visible,
  _newVisible => {
    _newVisible
      ? document.body.addEventListener("click", closeMenu)
      : document.body.removeEventListener("click", closeMenu);
  }
);

const openMenu = e => {
  // 鼠标相对坐标
  let x = e.clientX;
  let y = e.clientY;
  // 菜单相对鼠标位置，设置菜单可见
  rightClickMenu.left = x - 95;
  rightClickMenu.top = y - 22;
  rightClickMenu.visible = true;
  // 鼠标相对于echart的坐标转换为echart逻辑坐标,获取横坐标X
  if (rightClickMenu.chart !== null) {
    let pointInGrid = rightClickMenu.chart.convertFromPixel(
      { seriesIndex: 0 },
      [e.offsetX, e.offsetY]
    );
    // 根据echart坐标获取对应值
    rightClickMenu.time =
      rightClickMenu.chart.getOption().xAxis[0].data[pointInGrid[0]];
  }
};

const closeMenu = () => {
  rightClickMenu.visible = false;
};

// 弹出图片查看组件
const openImageView = () => {
  let query: getFileListParam = {
    equipmentId: pageData.searchTestInfo.tester,
    date: pageData.searchTestInfo.testTime,
    time: rightClickMenu.time
  };
  if (query.time === null || query.time === "") {
    errorMessage("Empty Chart");
    return;
  }
  ocrIcsTxtAPI.getFileListByTime(query).then((res: FileVO[] | string) => {
    if (res !== "fail" && res.length > 0) {
      let fileArr = res as FileVO[];
      rightClickMenu.fileVoArr = fileArr;
      doOpen();
    } else {
      errorMessage("No Image Found");
    }
  });
};

const doOpen = () => {
  viewimage.value.setVisible();
};
</script>

<template>
  <div>
    <div
      id="main1"
      style="width: 100%; height: 400px"
      @contextmenu.prevent="openMenu($event)"
    />
    <div>
      <ul
        v-show="rightClickMenu.visible"
        :style="{
          left: rightClickMenu.left + 'px',
          top: rightClickMenu.top + 'px'
        }"
        class="contextmenu"
      >
        <li @click="openImageView">View Image</li>
      </ul>
      <view-image :file-list="rightClickMenu.fileVoArr" ref="viewimage" />
    </div>
  </div>
</template>

<style scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  position: fixed;
  z-index: 3000;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}

.contextmenu li:hover {
  background: #eee;
}
</style>
