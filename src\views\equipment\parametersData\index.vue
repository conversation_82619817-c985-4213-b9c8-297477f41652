<script setup lang="ts">
import { onMounted, reactive } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { transformI18n } from "/@/plugins/i18n";
import detailChart from "./components/detailChart.vue";
import { equipParameterAPI } from "/@/api/equipment/equipParameter";
import dayjs from "dayjs";

const pageData = reactive({
  loading: false,
  selection: [],
  dataList: [],
  groupId: "1",
  parameterList: [], // 参数列表
  dateRange: [], // 时间范围
  searchInfo: {
    startTime: "", // 开始时间(含时分秒)
    endTime: "", // 结束时间(含时分秒)
    searchText: "", // 参数
    sortColumn: [],
    sortType: "",
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  }
});

onMounted(() => {
  pageData.dateRange = [
    dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    dayjs().format("YYYY-MM-DD HH:mm:ss")
  ];
  loadPage();
});

const loadPage = () => {
  pageData.parameterList = ["yb101_PC_VSBM_Potting_POTTING_CYCLE_TIME"];
  pageData.searchInfo.startTime = pageData.dateRange[0];
  pageData.searchInfo.endTime = pageData.dateRange[1];
  getPage();
  // 获取参数列表
  // equipParameterAPI
  //   .getList()
  //   .then((paramRes: any) => {
  //     if (paramRes !== "fail") {
  //       pageData.parameterList = paramRes;
  //     }
  //     pageData.searchInfo.startTime = pageData.dateRange[0];
  //     pageData.searchInfo.endTime = pageData.dateRange[1];
  //     getPage();
  //   })
  //   .finally(() => (pageData.loading = false));
};

// 监听日期范围变化事件
const dateRangeChange = (dateRange: [string, string]) => {
  pageData.dateRange = dateRange;
  getPage();
};

const getPage = () => {
  pageData.loading = true;
  pageData.dataList = [];
  equipParameterAPI
    .getSpecifyPage(pageData.searchInfo)
    .then((res: any) => {
      if (res !== "fail") {
        pageData.dataList = res.records;
        pageData.searchInfo.total = Number(res.total);
      }
    })
    .finally(() => (pageData.loading = false));
};

const sizeChange = pageSize => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = pageNum => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const onSearch = () => {
  getPage();
};

const resetSearch = () => {
  pageData.searchInfo.searchText = "";
  pageData.searchInfo.startTime = pageData.dateRange[0];
  pageData.searchInfo.endTime = pageData.dateRange[1];
};

// 日期选择限制 - 不能超过当天
const disabledDate = time => {
  return time.getTime() > Date.now();
};

const handleSortChange = ({ _column, prop, order }) => {
  if (prop === "readTime") {
    prop = "read_time";
  }
  pageData.searchInfo.sortColumn = prop === null ? [] : [prop];
  pageData.searchInfo.sortType = order == "ascending" ? "ASC" : "DESC";
  getPage();
};
</script>

<template>
  <div>
    <el-card class="query-card">
      <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
        <el-form-item label="Time Range">
          <el-date-picker
            v-model="pageData.dateRange"
            type="datetimerange"
            range-separator="To"
            start-placeholder="Start time"
            end-placeholder="End time"
            :disabled-date="disabledDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="dateRangeChange"
          />
        </el-form-item>
        <el-form-item label="Parameter">
          <el-select
            v-model="pageData.searchInfo.searchText"
            placeholder="Select Parameter"
            clearable
            class="!w-[250px]"
          >
            <el-option
              v-for="item in pageData.parameterList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            :loading="pageData.loading"
            @click="onSearch"
          >
            {{ transformI18n("query.search") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('refresh')"
            :loading="pageData.loading"
            @click="resetSearch"
          >
            {{ transformI18n("query.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <el-row align="middle">
        <el-col :span="24" align="left">
          <span style="font-weight: bold; font-size: 20px"
            >Equipment Parameters</span
          >
        </el-col>
      </el-row>
      <el-divider />
      <el-table
        v-loading="pageData.loading"
        :data="pageData.dataList"
        style="width: 100%"
        row-key="paramId"
        @sort-change="handleSortChange"
        border
      >
        <el-table-column type="expand">
          <template #default="props">
            <detail-chart
              :row="props.row"
              :date-range="pageData.dateRange"
              v-if="!props.row.dataType.match('string')"
            />
            <span v-if="props.row.dataType.match('string')">{{
              props.row.readData
            }}</span>
          </template>
        </el-table-column>

        <!-- 新增Line ID列 -->
        <el-table-column
          prop="clientCode"
          label="Line ID"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="variable"
          label="Parameter"
          sortable="custom"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="variableName"
          label="Parameter Name"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="dataType"
          label="Data Type"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="readData"
          label="Current Value"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="readTime"
          label="Last Change On"
          align="center"
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column prop="address" label="address" v-if="false" />
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped>
.el-card {
  margin: 0px;
  padding: 0px;
  align-items: center;
  justify-content: center;
  width: 100% !important;
}

.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.el-pagination {
  margin-top: 10px;
}
</style>
