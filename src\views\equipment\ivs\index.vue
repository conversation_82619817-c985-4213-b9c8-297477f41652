<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { iVSPosAPI } from "/@/api/local/ivsPos";
import Local from "./local/index.vue";
import Server from "./server-web/index.vue";

// 是否需要展示
const isLocal = ref(0);

// 获取数据的函数，传递时间参数
onBeforeMount(() => {
  iVSPosAPI
    .getIVSInfo()
    .then(res => {
      if (res === "empty") {
        isLocal.value = 2;
      } else {
        isLocal.value = 1;
      }
    })
    .catch(_err => {
      isLocal.value = 2;
    });
});
</script>

<template>
  <div>
    <div v-if="isLocal === 2">
      <Server />
    </div>
    <div v-if="isLocal === 1">
      <Local />
    </div>
  </div>
</template>
