import { http } from "/@/utils/http";

class AbAutoAdjustAPI {
  getabInfo(): any {
    return http.request("get", "http://localhost:8080/client/ab/info", null);
  }

  fetchStatus(): any {
    return http.request("get", "http://localhost:8080/client/ab/status", null);
  }

  fetchManuelData(params): any {
    return http.request("get", "http://localhost:8080/client/ab/manuel", {
      params
    });
  }

  writePlcData(data): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ab/manuel/write",
      { data: data }
    );
  }

  updateFormula(data): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ab/manuel/update/formula",
      { data: data }
    );
  }

  fetchData(params): any {
    return http.request("get", "http://localhost:8080/client/ab/data", {
      params
    });
  }

  fetchLatestData(params): any {
    return http.request("get", "http://localhost:8080/client/ab/data/latest", {
      params
    });
  }

  fetchRules(params): any {
    return http.request("get", "http://localhost:8080/client/ab/rules", {
      params
    });
  }

  toggleRule(rule): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ab/rules/toggle",
      { data: rule }
    );
  }

  switchRule(rule): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ab/rules/switch",
      { data: rule }
    );
  }

  fetchSpec(): any {
    return http.request("get", "http://localhost:8080/client/ab/spec", null);
  }

  updateSpec(data): any {
    return http.request("post", "http://localhost:8080/client/ab/spec/update", {
      data: data
    });
  }

  fetchHistory(params): any {
    return http.request("get", "http://localhost:8080/client/ab/history", {
      params
    });
  }

  fetchLastestHistory(params): any {
    return http.request(
      "get",
      "http://localhost:8080/client/ab/history/latest",
      {
        params
      }
    );
  }

  getabKv8000WriteFlag(): any {
    return http.request("get", "http://localhost:8080/client/ab/getFlag", null);
  }

  writeabKv8000WriteFlag(): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ab/writeFlag",
      null
    );
  }
}

export const abAutoAdjustAPI = new AbAutoAdjustAPI();
