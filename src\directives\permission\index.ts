import { usePermissionStoreHook } from "/@/store/modules/permission";
import { Directive } from "vue";
import type { DirectiveBinding } from "vue";

export const auth: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding;
    if (value) {
      // const authRoles = value;
      // const hasAuth = JSON.stringify(
      //   usePermissionStoreHook().getButtonAuth
      // ).includes(authRoles);
      // if (!hasAuth) {
      //   el.parentNode.removeChild(el);
      // }

      const requiredRoles = value;
      const ownedAuths = JSON.stringify(usePermissionStoreHook().getButtonAuth);
      // 需要的权限, 有其中之一便判断为有权访问
      if (requiredRoles.filter(ra => ownedAuths.includes(ra)).length == 0) {
        el.parentNode.removeChild(el);
      }
    } else {
      throw new Error("need roles! Like v-auth=\"['admin','test']\"");
    }
  }
};
