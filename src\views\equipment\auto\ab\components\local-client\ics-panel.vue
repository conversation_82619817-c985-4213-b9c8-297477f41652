<script setup lang="ts">
import { onBeforeMount, onMounted, ref } from "vue";
import icsChart from "./ics-chart.vue";
import { abAutoAdjustAPI } from "/@/api/local/abAutoAdjust";
import { emitter } from "/@/utils/mitt";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object
  },
  pageWidth: {
    require: true,
    type: Number
  },
  pageHeight: {
    require: true,
    type: Number
  }
});

const leftTRightF = ref(true);

const onOffRule = ref({ enabled: "0" });

onBeforeMount(() => {
  leftTRightF.value = props.icsEquipInfo.clientCode.includes("-L-");
});

onMounted(() => {
  getRule();
});

const getRule = () => {
  abAutoAdjustAPI
    .fetchRules({ equipId: props.icsEquipInfo.id, type: 4 })
    .then((res: any) => {
      if (res !== "fail" && res.length > 0) {
        onOffRule.value = res[0];
      }
    })
    .catch(_err => {
      console.log("error");
    });
};

const switchRule = () => {
  abAutoAdjustAPI
    .switchRule(onOffRule.value)
    .then((_res: any) => {
      getRule();
    })
    .catch(_err => {
      console.log("error");
    });
};

const chart1 = ref(null);
const chart2 = ref(null);

emitter.on("reloadAutoIcsCharts", () => {
  setTimeout(() => {
    chart1.value.callReload();
    chart2.value.callReload();
  }, 1000);
});
</script>

<template>
  <el-row align="middle">
    <span style="font-weight: bold; font-size: 16px">
      {{ leftTRightF ? "Auto Adjust AB-L" : "Auto Adjust AB-R" }}
    </span>
    <el-switch
      v-model="onOffRule.enabled"
      @change="switchRule"
      inline-prompt
      style="
        --el-switch-on-color: #13ce66;
        --el-switch-off-color: #ff4949;
        margin-left: 20px;
      "
      active-value="1"
      inactive-value="0"
      active-text="On"
      inactive-text="Off"
    />
  </el-row>

  <icsChart
    :equip-id="props.icsEquipInfo.id"
    :mark="leftTRightF ? 'L' : 'R'"
    :mark-num="'A1'"
    :page-width="props.pageWidth"
    :page-height="props.pageHeight"
    ref="chart1"
  />
  <icsChart
    :equip-id="props.icsEquipInfo.id"
    :mark="leftTRightF ? 'L' : 'R'"
    :mark-num="'B1'"
    :page-width="props.pageWidth"
    :page-height="props.pageHeight"
    ref="chart2"
  />
</template>

<style scoped></style>
