<script setup lang="ts">
import { ref } from "vue";
import { FileVO } from "/@/api/model/equipment/ics_txt_statistic";

defineOptions({
  name: "ViewImage"
});

const props = defineProps({
  fileList: {
    type: Object as PropType<FileVO[]>,
    required: true
  }
});

const visible = ref(false);

const setVisible = () => {
  visible.value = true;
};

defineExpose({
  setVisible
});
</script>

<template>
  <div>
    <el-dialog v-model="visible" title="Image Viewer">
      <el-carousel
        type="card"
        height="400px"
        :autoplay="false"
        :loop="false"
        trigger="click"
      >
        <el-carousel-item
          v-for="fileVO in props.fileList"
          :key="fileVO.downloadUrl"
          :label="fileVO.minIOUrl"
        >
          <el-image
            :src="fileVO.downloadUrl"
            :preview-src-list="new Array(fileVO.downloadUrl)"
            fit="cover"
            :preview-teleported="true"
          />
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-dialog .el-dialog__body {
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-carousel__item {
  color: #475669;
  margin: 0px;
  text-align: center;
  width: 50%;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.el-image {
  width: 100%;
  height: 400px;
}
</style>
