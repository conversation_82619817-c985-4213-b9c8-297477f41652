<script setup lang="ts">
import { ivsTxtStatisticAPI } from "/@/api/equipment/ivsTxtStatistic";
import { onMounted, reactive } from "vue";
import {
  paramList,
  ivsTxtDetailVO
} from "/@/api/model/equipment/ivs_txt_statistic";
import { Page } from "/@/api/model/domain";

defineOptions({
  name: "ivsTxtDetail"
});

const pageData = reactive<{
  componentKey: number;
  loading: boolean;
  selection: any[];
  testList: ivsTxtDetailVO[];
  searchTestInfo: paramList;
}>({
  componentKey: 1,
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    equipmentId: null,
    padResult: null,
    start: "2022-09-16",
    end: "2022-09-16",
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  }
});

const getPage = () => {
  if (pageData.searchTestInfo.equipmentId == null) {
    return;
  }
  pageData.loading = true;
  ivsTxtStatisticAPI
    .page(pageData.searchTestInfo)
    .then((res: Page<ivsTxtDetailVO[]> | string) => {
      if (res !== "fail") {
        pageData.testList = (res as Page<ivsTxtDetailVO[]>).records;
        pageData.searchTestInfo.total = Number(
          (res as Page<ivsTxtDetailVO[]>).total
        );
      }
    })
    .finally(() => (pageData.loading = false));
};

onMounted(() => {
  pageData.searchTestInfo = props.txt;
  getPage();
});

const props = defineProps({
  txt: {
    type: Object as PropType<paramList>,
    required: true
  }
});

const handelSelectionChange = val => {
  pageData.selection = val;
};

const sizeChange = (pageSize: number) => {
  pageData.searchTestInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchTestInfo.pageNum = pageNum;
  getPage();
};
</script>

<template>
  <div>
    <el-table
      v-loading="pageData.loading"
      :data="pageData.testList"
      style="width: 100%"
      row-key="id"
      border
      @selection-change="handelSelectionChange"
    >
      <el-table-column
        sortable
        resizable
        :show-overflow-tooltip="true"
        type="selection"
      />
      <el-table-column
        prop="collectTime"
        label="收集时间"
        sortable
        width="160px"
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="padResult"
        label="位置"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result0"
        label="P1"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result1"
        label="P2"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result2"
        label="P3"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result3"
        label="P4"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result4"
        label="P5"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result5"
        label="P6"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result6"
        label="P7"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result7"
        label="P8"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result8"
        label="P9"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="result9"
        label="P10"
        sortable
        resizable
        :show-overflow-tooltip="true"
      />
    </el-table>
    <el-pagination
      v-model:currentPage="pageData.searchTestInfo.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageData.searchTestInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageData.searchTestInfo.total"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<style scoped></style>
