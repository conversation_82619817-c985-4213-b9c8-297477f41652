import { BaseQuery } from "../domain";

export interface KeyenceQuery extends BaseQuery {
  equipmentId?: string;
  paramId?: string;
  collectDate?: string;
  startTime: string;
  endTime: string;
}

export interface KeyenceVO {
  id: string;
  paramName: string;
  englishName: string;
  instruct: string;
  originalData: string;
  translateData: string;
  collectDate: string;
  continueDate: string;
}

export interface KeyenceParamVO {
  id: string;
  englishName: string;
  paramName: string;
}

export interface EquipmentVO {
  id: string;
  equipmentName: string;
  equipmentType: string;
  parentId: string;
  parentName: string;
}

export interface LineChartVO {
  xLine: Array<string>;
  yLine: Array<string>;
}
