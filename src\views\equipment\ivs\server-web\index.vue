<script setup lang="ts">
import { onBeforeMount, ref, watch } from "vue";
import { ivsPosAPIs } from "/@/api/ivs/ivsPos";
import Chart from "./chart.vue"; // Import chart component
import pad_chart from "./pad_chart.vue";

// Reactive variables for storing different chart data
const chartDataIVSL = ref<any[]>([]);
const chartDataIVSR = ref<any[]>([]);
const chartDataPadL = ref<any[]>([]);
const chartDataPadR = ref<any[]>([]);

const xAxisDataAL = ref<string[]>([]);
const xAxisDataAR = ref<string[]>([]);
const xAxisDataBL = ref<string[]>([]);
const xAxisDataBR = ref<string[]>([]);

const selectButton = ref("current");

interface SearchInfo {
  groupId: string;
  groupName: string;
}

const props = defineProps<{
  groupList: Array<{ groupId: string; groupName: string }>;
  searchInfo: SearchInfo;
}>();

// Store the selected date and time range
const selectedDateRange = ref<string[]>([]);

// Get data function, passing time range as parameters
const fetchData = async (startDateTime: string, endDateTime: string) => {
  ivsPosAPIs
    .getIVSData({
      start_time: startDateTime,
      end_time: endDateTime,
      groupId: props.searchInfo.groupId
    })
    .then((res: any) => {
      if (res !== "fail") {
        processIVSChartData(res);
      }
    });
  ivsPosAPIs
    .getPadData({
      start_time: startDateTime,
      end_time: endDateTime,
      groupId: props.searchInfo.groupId
    })
    .then((res: any) => {
      if (res !== "fail") {
        processPadChartData(res);
      }
    });
};

// Data processing functions
const processIVSChartData = (res: any) => {
  chartDataIVSL.value = getSeriesFromResData("L-IVS", res["L-IVS"]);
  chartDataIVSR.value = getSeriesFromResData("R-IVS", res["R-IVS"]);
};

const processPadChartData = (res: any) => {
  chartDataPadL.value = getSeriesFromResData("L-PAD", res["L-PAD"]);
  chartDataPadR.value = getSeriesFromResData("R-PAD", res["R-PAD"]);
};

// Helper function for chart data formatting
const getSeriesFromResData = (chartName: string, series: any) => {
  const seriesData: any[] = [];

  seriesData.push({
    name: `data`,
    type: "bar",
    stack: `data`,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null,
    data: series || []
  });

  return seriesData;
};

// Disable future dates
const disabledDate = (date: Date) => {
  return date > new Date();
};
const getNowDate = (
  type,
  day: number | null = 0,
  toNow: boolean | null = true
) => {
  selectButton.value = type;
  // 获取当前时间
  let now = new Date();

  // 对于1hr和4hrs的特殊处理
  if (type === "1hr" || type === "4hrs") {
    const hours = type === "1hr" ? 1 : 4;
    const endTime = new Date();
    const startTime = new Date(now.getTime() - hours * 60 * 60 * 1000);

    selectedDateRange.value = [
      formatDateTime(startTime),
      formatDateTime(endTime)
    ];

    fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
    return;
  }

  let shiftStart, shiftEnd;
  // 特殊处理：当day为1时，返回昨天7点到今天7点
  if (day === 1) {
    const today = new Date();
    shiftEnd = new Date(today.setHours(7, 0, 0, 0));
    shiftStart = new Date(today);
    shiftStart.setDate(shiftStart.getDate() - 1);
    shiftStart.setHours(7, 0, 0, 0);
    selectedDateRange.value = [
      formatDateTime(shiftStart),
      formatDateTime(shiftEnd)
    ];
    fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
    return;
  }

  // 原有逻辑保持不变
  if (now.getHours() >= 7 && now.getHours() < 19) {
    // 当前为白班 7:00-19:00
    shiftStart = new Date(now.setHours(7, 0, 0, 0));
    shiftEnd = new Date(now.setHours(19, 0, 0, 0));
  } else {
    // 当前为夜班 19:00-次日 7:00
    shiftStart = new Date(
      now.getHours() < 7 ? now.setDate(now.getDate() - 1) : now
    ).setHours(19, 0, 0, 0);
    shiftEnd = new Date(
      now.getHours() < 7 ? now : now.setDate(now.getDate() + 1)
    ).setHours(7, 0, 0, 0);
  }

  if (day) {
    // 结束日期是否到当前,上个班次
    if (!toNow) {
      shiftStart.setHours(shiftStart.getHours() - day * 24);
      shiftEnd.setHours(shiftEnd.getHours() - day * 24);
    } else {
      shiftStart.setHours(shiftStart.getHours() - day * 24 + 12);
    }
  }
  console.log(shiftStart.toLocaleString(), shiftEnd.toLocaleString());
  selectedDateRange.value = [
    formatDateTime(shiftStart),
    formatDateTime(shiftEnd)
  ];

  fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
};
// 自定义日期格式化函数，确保所有部分都以两位数显示
const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要+1
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

onBeforeMount(() => {
  setTimeout(() => {
    getNowDate(selectButton.value);
  }, 50);
});

watch(
  () => props.searchInfo.groupId,
  () => {
    setTimeout(() => {
      fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
    }, 50);
  }
);
</script>

<template>
  <div>
    <div>
      <!-- Buttons for Shift and Time Range Selection -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <el-row class="form-row">
              <el-form-item>
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === '1hr' }"
                  @click="getNowDate('1hr', 1 / 4)"
                  >1hr</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === '4hrs' }"
                  @click="getNowDate('4hrs', 4 / 24)"
                  >4hrs</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === 'current' }"
                  @click="getNowDate('current')"
                  >Current Shift</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === 'last' }"
                  @click="getNowDate('last', 0.5, false)"
                  >Last Shift</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === 'daily' }"
                  @click="getNowDate('daily', 1)"
                  >Daily</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === 'weekly' }"
                  @click="getNowDate('weekly', 7)"
                  >Weekly</el-button
                >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === 'monthly' }"
                  @click="getNowDate('monthly', 30)"
                  >Monthly</el-button
                >
              </el-form-item>
            </el-row>
            <!-- Date Picker -->
            <el-row class="form-row">
              <el-form-item>
                <el-date-picker
                  v-model="selectedDateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期时间"
                  end-placeholder="结束日期时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  :disabled-date="disabledDate"
                  @change="value => fetchData(value[0], value[1])"
                  class="date-picker"
                />
              </el-form-item>
            </el-row>
          </el-form>
        </el-col>
      </el-row>

      <!-- Render four charts -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-al"
            :xLine="xAxisDataAL"
            :yLine="chartDataIVSL"
            title="L-IVS defect distribution by tray position"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <pad_chart
            chartId="chart-bl"
            :xLine="xAxisDataBL"
            :yLine="chartDataPadL"
            title="L-T-defect distribution by SBB nozzle hole number"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar"
            :xLine="xAxisDataAR"
            :yLine="chartDataIVSR"
            title="R-IVS defect distribution by tray position"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <pad_chart
            chartId="chart-br"
            :xLine="xAxisDataBR"
            :yLine="chartDataPadR"
            title="R-T-defect distribution by SBB nozzle hole number"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.chart-row {
  margin-bottom: 20px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.form-row {
  margin-bottom: 10px;
  width: 100%;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
  margin-right: 5px;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
}
</style>
