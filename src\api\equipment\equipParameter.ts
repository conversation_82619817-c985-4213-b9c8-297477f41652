import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/equipment/param",
  PARAM_DATA = "/data",
  PARAM_list = "/list",
  SPECIFT_PAGE = "/list/specify_page",
  SPECIFT_DATA = "/specify_data"
}

class EquipParameterAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getData<T>(query): Promise<T> {
    return this.get<T>(API.PARAM_DATA, query);
  }

  getList<T>(): Promise<T> {
    return this.get<T>(API.PARAM_list);
  }

  getSpecifyPage<T>(query): Promise<T> {
    return this.post<T>(API.SPECIFT_PAGE, query);
  }

  getSpecifyData<T>(query): Promise<T> {
    return this.get<T>(API.SPECIFT_DATA, query);
  }
}

export const equipParameterAPI = new EquipParameterAPI();
