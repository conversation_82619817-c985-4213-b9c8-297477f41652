<script setup lang="ts">
import { onBeforeMount, onMounted, ref } from "vue";
//import icsStatus from "./ics-status.vue";
import icsHistory from "./ics-history.vue";
import adjustRules from "./adjust-rules.vue";
import manualSet from "./manual-set.vue";
import dayjs from "dayjs";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const selectedDates = ref();

const icsHistoryRef = ref(null);

onBeforeMount(() => {
  selectedDates.value = [dayjs().format("YYYY-MM-DD")];
});

onMounted(() => {});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const callReload = () => {
  icsHistoryRef.value.reloadHistory(selectedDates.value);
};
</script>

<template>
  <el-row class="status-board">
    <el-col :span="16">
      <el-row>
        <el-col>
          <span style="font-weight: bold; font-size: 16px; margin-left: 5px">
            Control Panel:
          </span>
          <!-- <icsStatus /> -->
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <adjustRules />
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <manualSet :icsEquipInfo="props.icsEquipInfo" />
        </el-col>
      </el-row>
    </el-col>

    <el-col :span="8">
      <el-row>
        <el-col>
          <el-row align="middle">
            <el-col :span="12">
              <span
                style="font-weight: bold; font-size: 16px; margin-left: 10px"
              >
                Adjust History:
              </span>
            </el-col>
            <el-col :span="12" align="right">
              <el-date-picker
                v-model="selectedDates"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                type="dates"
                placeholder="Pick one or more dates"
                :clearable="false"
                @change="callReload"
                style="width: 160px"
              />
            </el-col>
          </el-row>
          <icsHistory :selected-dates="selectedDates" ref="icsHistoryRef" />
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>

<style scoped>
.status-board {
  min-width: 860px;
  margin-bottom: 30px;
}

.el-row {
  margin: 0;
  padding: 0;
}
</style>
