import BaseRequest from "../base";
enum API {
  BASE_URL = "/equipment"
}

class EquipmentAPI extends BaseRequest {
  getJarIdUrl: string;
  getTypeUrl: string;
  syncOcrUrl: string;

  constructor() {
    super();
    this.getJarIdUrl = "/getJarIdList";
    this.getTypeUrl = "/getEquipmentTypeList";
    this.syncOcrUrl = "/syncOcrIcsEquip";
  }

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getJarIdList<T>(): Promise<T> {
    return this.get<T>(this.getJarIdUrl);
  }

  getEquipmentTypeList<T>(): Promise<T> {
    return this.get<T>(this.getTypeUrl);
  }
  syncOcrIcsEquip<T>(): Promise<T> {
    return this.get(this.syncOcrUrl);
  }
}

export const equipmentAPI = new EquipmentAPI();
