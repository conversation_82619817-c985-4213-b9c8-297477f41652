<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";

// 定义 props 类型
const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array as () => (string | number)[],
    required: true
  },
  yLine: {
    type: Array,
    required: true
  },
  yAxisMin: {
    type: Number,
    default: 0
  },
  yAxisMax: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    required: true
  }
});
let chart: echarts.ECharts | null = null; // 存储ECharts实例;
// let yAxisMin = 0;
// let yAxisMax = 0;

// //计算 y 轴最小值和最大值
// const calculateYAxisRange = () => {
//   const allData = props.yLine.flatMap((series: any) => series.data);
//   if (allData.length > 0) {
//     yAxisMin = Math.min(...allData);
//     yAxisMax = Math.max(...allData);
//   } else {
//     yAxisMin = 0;
//     yAxisMax = 0;
//   }
// };

const initChart = () => {
  if (!chart) return;

  //calculateYAxisRange();
  let option: EChartsOption;
  option = {
    title: {
      text: props.title, // 标题内容
      left: "center", // 将标题居中
      //top: "2%", // 距顶部距离
      //bottom: "2%", //底部距离
      textStyle: {
        color: "black", // 标题颜色
        fontSize: 18, // 标题字体大小
        fontWeight: "bold", // 加粗字体
        fontStyle: "normal", // 正楷
        fontFamily: "Arial, sans-serif", // 字体
        textBorderColor: "rgba(255, 255, 255, 0.6)", // 标题边框颜色
        textBorderWidth: 0, // 标题边框宽度
        shadowColor: "rgba(0, 0, 0, 0.6)" // 标题阴影颜色
      },
      //backgroundColor: "rgba(245, 245, 245, 0.6)", // 标题背景色（带透明度）
      borderRadius: 10, // 圆角背景
      padding: [10, 15], // 内边距
      borderColor: "rgba(255, 255, 255, 0.6)", // 边框颜色
      borderWidth: 1 // 边框宽度
    },
    backgroundColor: "#fff", // 设置图表背景颜色为白色
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: props.yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)", // 背景透明度
      borderColor: "#333", // 边框颜色
      borderWidth: 1,
      textStyle: {
        color: "#fff", // 字体颜色
        fontSize: 14 // 字体大小
      },
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(0,0,0,0.1)" // 指示线的阴影效果
        }
      },
      formatter: function (params) {
        // 获取 x 轴的值（所有项的 name 都应该是相同的）
        let xValue = params[0].name;
        // 显示 x 轴值在最上面
        let tooltipContent = `${xValue}<br/>`;
        // 展示每个系列的数据
        tooltipContent += params
          .map(item => {
            let value = "-";
            if (item.value) {
              value = item.value + "%";
            }
            return `${item.marker} </span>${item.seriesName} <span style="padding-left: 15px;"> ${value}</strong>`;
          })
          .join("<br/>");

        return tooltipContent;
      }
    },

    legend: {
      top: "8%",
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      itemGap: 10 // 图例项之间的间距
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    grid: {
      top: "20%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xLine
      }
    ],
    yAxis: [
      {
        axisLabel: {
          formatter: function (value) {
            return value + "%";
          }
        },
        type: "value",
        scale: true,
        min: null,
        max: null
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 15,
        handleSize: "150%", // 增加手柄的大小
        handleStyle: {
          color: "#fff", // 手柄的颜色
          borderColor: "#5D92F4" // 手柄的边框颜色
        },
        textStyle: {
          color: "#5D92F4" // 滚动条上的文字颜色
        },
        borderColor: "#5D92F4", // 滚动条边框颜色
        fillerColor: "rgba(93, 146, 244, 0.5)", // 选中区域的颜色
        backgroundColor: "rgba(221, 221, 221, 0.2)" // 未选中区域的背景色
      }
    ],
    series: props.yLine
    // .map(seriesData =>
    //   typeof seriesData === "object" && seriesData !== null
    //     ? {
    //         ...seriesData,
    //         type: "bar",
    //         barWidth: "50%",
    //         label: {
    //           show: true,
    //           position: "top",
    //           color: "#000",
    //           fontSize: 14,
    //           fontWeight: "bolder"
    //         }
    //       }
    //     : {}
    // )
  };
  option && chart.setOption(option);
};

onMounted(() => {
  const chartElement = document.getElementById(props.chartId) as HTMLElement;
  if (chartElement) {
    chart = echarts.init(chartElement);
    initChart();
  }
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine
  }),
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div :id="chartId" class="chart-container" />
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 这里可以根据需要调整高度 */
}
</style>
