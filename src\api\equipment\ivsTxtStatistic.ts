import BaseRequest from "../base";
enum API {
  BASE_URL = "/ivsTxtStatistics"
}

class IvsTxtStatisticAPI extends BaseRequest {
  private getData: string;
  private getEquipList: string;

  constructor() {
    super();
    this.getData = "/list";
    this.getEquipList = "/getEquipmentSelectList";
  }

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  list<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getData, query);
  }

  getEquipmentSelectList<T>(): Promise<T> {
    return this.get<T>(this.getEquipList);
  }
}

export const ivsTxtStatisticAPI = new IvsTxtStatisticAPI();
