import BaseRequest from "../base";
enum API {
  BASE_URL = "/client/combine",
  page = "/list/page",
  DOWNLOAD = "/api/client/combine/download?"
}

class HARMAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getDownloadUrl(): string {
    return API.DOWNLOAD;
  }

  download(data: any) {
    return this.get(API.DOWNLOAD, data);
  }

  getPage(data: any) {
    return this.post(API.page, data);
  }
}

export const harmAPI = new HARMAPI();
