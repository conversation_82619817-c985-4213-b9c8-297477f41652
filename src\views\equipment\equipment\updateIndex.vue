<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    @close="props.closeCallBack"
    size="50%"
  >
    <template #header>
      <h4>
        {{
          transformI18n("buttons.hsupdate") + pageData.SaveParam.equipmentName
        }}
      </h4>
    </template>
    <template #default>
      <div>
        <el-form
          ref="formData"
          :inline="true"
          :model="pageData.SaveParam"
          label-width="150px"
          :rules="rules"
          status-icon
        >
          <el-form-item
            :label="transformI18n('equipment.equipmentName')"
            prop="equipmentName"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.equipmentName" />
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.equipmentCode')"
            prop="equipmentCode"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.equipmentCode" />
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.equipmentType')"
            prop="equipmentType"
            style="width: 80%"
          >
            <el-select
              v-model="pageData.SaveParam.equipmentType"
              style="width: 100%"
            >
              <el-option
                v-for="item in props.EquipmentType"
                :key="item.value"
                :label="transformI18n(item.name)"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.groupName')"
            prop="groupName"
            style="width: 80%"
          >
            <el-select v-model="pageData.SaveParam.groupId" style="width: 100%">
              <el-option
                v-for="item in props.equipGroupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.address')"
            prop="address"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.address" />
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.jarId')"
            v-if="pageData.SaveParam.equipmentType == '1'"
            prop="jarId"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.jarId" />
          </el-form-item>
          <el-form-item
            :label="transformI18n('equipment.jarId')"
            v-if="
              pageData.SaveParam.equipmentType != '1' &&
              pageData.SaveParam.equipmentType != '6'
            "
            prop="parentId"
            style="width: 80%"
          >
            <el-select
              v-model="pageData.SaveParam.parentId"
              style="width: 100%"
            >
              <el-option
                v-for="item in pageData.JarDetailList"
                :key="item.id"
                :label="item.jarId"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            :label="transformI18n('equipment.fileUserDomain')"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserDomain"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.fileUserDomain" />
          </el-form-item>

          <el-form-item
            :label="transformI18n('equipment.fileUserAccount')"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserAccount"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.fileUserAccount" />
          </el-form-item>

          <el-form-item
            :label="transformI18n('equipment.fileUserPws')"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserPws"
            style="width: 80%"
          >
            <el-input v-model="pageData.SaveParam.fileUserPws" />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">{{
          transformI18n("buttons.hscancel")
        }}</el-button>
        <el-button type="primary" @click="confirmClick(formData)">{{
          transformI18n("buttons.hssave")
        }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { ElMessageBox, FormRules, FormInstance } from "element-plus";
import { equipmentAPI } from "/@/api/equipment/equipment";
import {
  EquipmentSaveParam,
  EquipmentJarId,
  Equipment,
  EquipGroupVo
} from "/@/api/model/equipment/equipment_model";
import { EntityType } from "/@/api/model/domain";
import { transformI18n } from "/@/plugins/i18n";

const drawer = ref(true);

onMounted(() => {
  pageData.updateId = props.entity.id;
  console.log(props.entity.equipmentType);
  pageData.SaveParam.id = props.entity.id;
  pageData.SaveParam.equipmentName = props.entity.equipmentName;
  pageData.SaveParam.equipmentType = props.entity.equipmentType + "";
  pageData.SaveParam.equipmentCode = props.entity.equipmentCode;
  pageData.SaveParam.groupId = props.entity.groupId;
  pageData.SaveParam.address = props.entity.address;
  pageData.SaveParam.jarId = props.entity.jarId;
  pageData.SaveParam.parentId = props.entity.parentId;
  pageData.SaveParam.fileUserDomain = props.entity.fileUserDomain;
  pageData.SaveParam.fileUserAccount = props.entity.fileUserAccount;
  pageData.SaveParam.fileUserPws = props.entity.fileUserPws;
  getJarIdList();
});

const formData = ref<FormInstance>();

const pageData = reactive<{
  JarDetailList: EquipmentJarId[];
  SaveParam: EquipmentSaveParam;
  updateId: string;
}>({
  JarDetailList: [],
  SaveParam: {
    id: null,
    equipmentName: null,
    jarId: null,
    equipmentType: "1",
    equipmentCode: null,
    address: null,
    parentId: null,
    fileUserDomain: null,
    fileUserAccount: null,
    fileUserPws: null
  },
  updateId: null
});

const getJarIdList = () => {
  equipmentAPI.getJarIdList().then((res: EquipmentJarId[] | string) => {
    if (res !== "fail") {
      pageData.JarDetailList = res as EquipmentJarId[];
    }
  });
};

function cancelClick() {
  drawer.value = false;
}

const confirmClick = async (fromData: FormInstance | undefined) => {
  if (!fromData) return;
  fromData.validate(valid => {
    if (valid) {
      ElMessageBox.confirm("Are you confirm to close?")
        .then(() => {
          equipmentAPI
            .updateById(pageData.updateId, pageData.SaveParam)
            .then(() => {
              drawer.value = false;
            })
            .catch()
            .finally();
        })
        .catch(() => {
          // catch error
        });
    } else {
      ElMessageBox.alert("The parameter is incomplete");
    }
  });
};

const props = defineProps({
  equipGroupList: {
    type: Array<EquipGroupVo>,
    default: null
  },
  entity: {
    type: Object as PropType<Equipment>,
    default: null
  },
  EquipmentType: {
    type: Array<EntityType>,
    default: []
  },

  closeCallBack: {
    type: Function
  }
});

const rules = reactive<FormRules>({
  equipmentName: [
    {
      required: true,
      message: "Please input Activity equipmentName",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  equipmentCode: [
    {
      required: true,
      message: "Please input Activity equipmentCode",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  equipmentType: [
    {
      required: true,
      message: "Please input Activity equipmentType",
      trigger: "blur"
    }
  ],
  parentId: [
    {
      required: true,
      message: "Please input Activity jarId",
      trigger: "blur"
    }
  ],
  jarId: [
    {
      required: true,
      message: "Please input Activity jarId",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  address: [
    {
      required: true,
      message: "Please input Activity address",
      trigger: "blur"
    },
    { min: 8, max: 40, message: "Length should be 8 to 40", trigger: "blur" }
  ],
  fileUserDomain: [
    {
      required: true,
      message: "Please input Activity fileUserDomain",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  fileUserAccount: [
    {
      required: true,
      message: "Please input Activity fileUserAccount",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  fileUserPws: [
    {
      required: true,
      message: "Please input Activity fileUserPws",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ]
});
</script>
