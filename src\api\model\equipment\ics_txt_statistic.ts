export interface paramList {
  collectDate?: string;
  equipmentId?: string;
  pos?: string;
}

export interface getFileListParam {
  equipmentId: string;
  date: string;
  time: string;
}

export interface icsTxtVO {
  result: string;
  result1: number;
  result2: number;
  result3: number;
  result4: number;
  result5: number;
  result6: number;
  result7: number;
  result8: number;
  result9: number;
  result10: number;
}

export interface seriesDataVO {
  name: string;
  type: string;
  stack: string;
  yAxisIndex: number;
  emphasis: {
    focus: "series";
  };
  markPoint: {
    data: [{ type: string; name: string }, { type: string; name: string }];
  };
  markLine: {
    data: [{ type: string; name: string }];
    label: { position: string; formatter: string };
  };
  data: number[];
}

export interface IcsEquipmentVO {
  id: string;
  equipmentName: string;
  equipmentType: string;
  parentId: string;
  parentName: string;
}

export interface IcsChartDataVO {
  xLine: Array<string>;
  yLine: Array<yLineDataVo>;
}

export interface yLineDataVo {
  name: string;
  data: Array<number>;
}

export interface FileVO {
  fileName: string;
  parentName: string;
  minIOUrl: string;
  downloadUrl: string;
}
