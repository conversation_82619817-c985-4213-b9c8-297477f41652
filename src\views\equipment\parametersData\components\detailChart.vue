<script setup lang="ts">
import { getCurrentInstance, onBeforeMount, onMounted, ref } from "vue";
import * as echarts from "echarts";
import { cloneDeep } from "lodash-unified";
import { equipParameterAPI } from "/@/api/equipment/equipParameter";

const props = defineProps({
  row: {
    require: true,
    type: Object
  },
  dateRange: {
    type: Array, // 确保类型为日期数组
    required: true
  }
});
// 时间
const dateRange = ref(props.dateRange);
// 修改为时间范围
let myChart = null;

const optionTemplate = {
  graphic: {
    type: "text",
    left: "center",
    top: "middle",
    silent: true,
    invisible: false,
    style: {
      fill: "#9d9d9d",
      fontWeight: "bold",
      text: "No Data",
      fontFamily: "Microsoft YaHei",
      fontSize: 25
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  toolbox: {
    show: true,
    feature: {
      dataZoom: {
        yAxisIndex: "none"
      },
      dataView: { readOnly: false },
      restore: {}
    }
  },
  dataZoom: [
    //滚动条
    {
      type: "slider",
      realtime: true,
      xAxisIndex: [0],
      bottom: "10",
      height: 10,
      borderColor: "rgba(0,0,0,0)",
      textStyle: {
        color: "#05D5FF"
      }
    }
  ],
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: [],
    axisLabel: {
      interval: "auto",
      rotate: 45,
      formatter: function (value, _index) {
        let label = value.substring(
          value.indexOf("-") + 1,
          value.lastIndexOf(".")
        );
        return label.replace("-", "/");
      }
    }
  },
  yAxis: [],
  series: []
};

onBeforeMount(() => {});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const { proxy } = getCurrentInstance();
let rowWidth = ref("");

onMounted(() => {
  setTimeout(() => {
    rowWidth.value = proxy.$el.clientWidth;
    initChart();
    console.log(props.dateRange);
    fetchData(props.dateRange);
  }, 100);
});

const initChart = () => {
  myChart = echarts.init(document.getElementById(props.row.paramId));
};

const fetchData = dateRange => {
  const startDate = dateRange[0];
  const endDate = dateRange[1];

  myChart.showLoading();
  equipParameterAPI
    .getSpecifyData({
      paramId: props.row.paramId,
      start: startDate,
      end: endDate
    })
    .then((res: any) => {
      if (res !== "fail") {
        console.log(res);
        setData(res);
      }
    })
    .catch(err => {
      console.log(err);
    })
    .finally(() => {
      myChart.hideLoading();
    });
};

const setData = data => {
  let hasData = data.series0Data.length > 0;
  let option = cloneDeep(optionTemplate);

  let series0 = {
    name: props.row.dataType,
    symbol: "emptyCircle",
    yAxisIndex: 0,
    zLevel: 0,
    z: 0,
    data: data.series0Data,
    type: "line"
  };
  option.xAxis.data = data.xAxisData;
  option.yAxis.push({
    position: "left",
    type: "value",
    name: props.row.dataType
  });
  option.series.push(series0);
  option.graphic.invisible = hasData;
  option && myChart.setOption(option);
};

const callReload = dateRange => {
  fetchData(dateRange);
};

const clickOnChart = () => {
  // Maybe add right click image view in future
};
</script>

<template>
  <el-row align="middle">
    <el-col align="middle" :span="12">
      <!-- 修改为时间范围选择器 -->
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="To"
        start-placeholder="Start time"
        end-placeholder="End time"
        :disabled-date="disabledDate"
        value-format="YYYY-MM-DD HH:mm:ss"
        @change="callReload"
      />
    </el-col>
    <el-col align="middle" :span="6">
      <span class="chartTitle">{{ props.row.variable }}</span>
    </el-col>
    <el-col align="middle" :span="6" />
  </el-row>
  <el-row align="middle">
    <el-col :span="24">
      <div
        @click.stop="clickOnChart"
        :id="props.row.paramId"
        :style="{
          width: rowWidth + 'px',
          height: '300px',
          margin: 0,
          padding: 0
        }"
      />
    </el-col>
  </el-row>
</template>

<style scoped>
.chartTitle {
  font-weight: bold;
  font-size: 1em;
}
</style>
