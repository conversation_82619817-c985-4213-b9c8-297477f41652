import BaseRequest from "../base";
import { BaseQuery } from "../model/domain";
enum API {
  BASE_URL = "/keyence"
}

class KeyenceAPI extends BaseRequest {
  private getParamList: string;
  private getEquipList: string;
  private getLineChart: string;

  constructor() {
    super();
    this.getParamList = "/getKeyenceParamByEquipmentId";
    this.getEquipList = "/getEquipmentSelectList";
    this.getLineChart = "/getKeyenceLineChart";
  }

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getEquipmentSelectList<T>(): Promise<T> {
    return this.get<T>(this.getEquipList);
  }

  getKeyenceParamByEquipmentId<T>(param: any): Promise<T> {
    return this.get<T>(this.getParamList, param);
  }

  getKeyenceLineChart<T, Q extends BaseQuery>(query: Q): Promise<T> {
    return this.post<T>(this.getLine<PERSON>hart, query);
  }
}

export const keyenceAPI = new KeyenceAPI();
