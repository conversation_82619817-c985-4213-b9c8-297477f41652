<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { hccmPosAPI } from "/@/api/local/hccmPos";
import Chart from "./chart.vue"; // Import chart component
import Dist_Chart from "./dist_chart.vue"; // Import chart component
import dayjs from "dayjs";
import AVG_Chart from "./avg_chart.vue"; // Import chart component

const AVG_xAxisData = ref<string[]>([]);
const AVG_chartDataDefct = ref<number[]>([]);

// Reactive variables for storing different chart data
const chartDataDefctL = ref<any[]>([]);
const chartDataDefctR = ref<any[]>([]);

const xAxisDataL = ref<string[]>([]);
const xAxisDataR = ref<string[]>([]);

const selectedDate = ref();

const chartOptions = {
  suspX: "Susp X(A-dim)",
  suspY: "Susp Y(B-dim)",
  suspAngle: "Suspension Angle",
  AFSDX: "AF-SDX(B-dim)",
  AFSDY: "AF-SDY(A-dim)",
  "BFSDX&BFSDY": "BFSDX&BFSDY",
  "SuspX&YByPos": "Susp X&Y by Pos"
};

// Selected chart type from dropdown
const selectedChartType = ref<string>("");

// Get data function, passing time range as parameters
const fetchData = async (date: string, type: string) => {
  hccmPosAPI.getData({ selected: date, type }).then((res: any) => {
    if (res !== "fail") {
      processIVSChartData(res);
    }
  });
};

// Data processing functions
const processIVSChartData = (res: any) => {
  if (selectedChartType.value === "BFSDX&BFSDY") {
    xAxisDataL.value = res["BFSDX"];
    xAxisDataR.value = res["BFSDY"];
  } else if (selectedChartType.value === "SuspX&YByPos") {
    chartDataDefctL.value = getSeriesFromPosData("SuspX", res["xoffset"]);
    chartDataDefctR.value = getSeriesFromPosData("SuspY", res["yoffset"]);
    xAxisDataL.value = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
  } else {
    chartDataDefctL.value = getSeriesFromResData(res["data"]);
    xAxisDataL.value = getxAxisData(res["data"]);
    AVG_xAxisData.value = res["mean_data"]["xAxis"];
    AVG_chartDataDefct.value = res["mean_data"]["mean"].map((item: string) =>
      Number(item)
    );
  }
};

const getxAxisData = (xAxis: any) => {
  return xAxis["xAxis"];
};
const getSeriesFromPosData = (name: string, series: any) => {
  const seriesData: any[] = [];
  seriesData.push({
    name: name,
    type: "line",
    stack: name,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null,
    data: series || []
  });

  return seriesData;
};
// Helper function for chart data formatting
const getSeriesFromResData = (series: any) => {
  const seriesData: any[] = [];
  Object.keys(series).forEach(key => {
    if (key === "xAxis") {
      return;
    }
    seriesData.push({
      name: key,
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: series[key] || []
    });
  });
  return seriesData;
};

// 日期选择限制，禁用今天以后的日期
const disabledDate = (date: Date) => {
  return date > new Date();
};
// 监听日期选择变化事件
const onDateChange = (date: string) => {
  selectedDate.value = date;
  fetchData(date, selectedChartType.value); // 当选择日期时，重新获取数据
};
// Handle dropdown selection changes
const onChartTypeChange = (type: string) => {
  selectedChartType.value = type;
  fetchData(selectedDate.value, type); // Fetch data for other chart types
};

onBeforeMount(() => {
  const today = [dayjs().format("YYYY-MM-DD")]; // 获取今天的日期
  selectedDate.value = today;
  selectedChartType.value = "suspX";
  fetchData(selectedDate.value, selectedChartType.value);
});
</script>

<template>
  <div>
    <div>
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <el-form-item label="选择日期">
              <el-date-picker
                v-model="selectedDate"
                type="dates"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                @change="onDateChange"
                class="date-picker"
              />
            </el-form-item>

            <el-form-item label="选择类型">
              <el-select
                v-model="selectedChartType"
                placeholder="请选择"
                @change="onChartTypeChange"
              >
                <el-option
                  v-for="(label, key) in chartOptions"
                  :key="key"
                  :label="label"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <!-- 只在 selectedChartType 不为 'BFSDX&AFSDY', 'SuspX&YByPos', 和 'BFSDX&BFSDY' 时展示 chart 组件 -->
          <AVG_Chart
            class="chart-row"
            v-if="
              selectedChartType !== 'BFSDX&AFSDY' &&
              selectedChartType !== 'SuspX&YByPos' &&
              selectedChartType !== 'BFSDX&BFSDY'
            "
            :chartId="'AVG' + selectedChartType"
            :xLine="AVG_xAxisData"
            :yLine="AVG_chartDataDefct"
            :title="'AVG' + chartOptions[selectedChartType]"
          />
          <chart
            v-if="
              selectedChartType !== 'BFSDX&AFSDY' &&
              selectedChartType !== 'SuspX&YByPos' &&
              selectedChartType !== 'BFSDX&BFSDY'
            "
            :chartId="selectedChartType"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            :title="chartOptions[selectedChartType]"
          />

          <!-- 'BFSDX&BFSDY' 类型下显示两个 Dist_Chart 组件 -->
          <Dist_Chart
            v-if="selectedChartType === 'BFSDX&BFSDY'"
            :chartId="'X' + selectedChartType"
            :xLine="xAxisDataL"
            :yLine="[]"
            :title="'BFSDX Chart'"
          />
          <Dist_Chart
            v-if="selectedChartType === 'BFSDX&BFSDY'"
            :chartId="'Y' + selectedChartType"
            :xLine="xAxisDataR"
            :yLine="[]"
            :title="'BFSDY Chart'"
          />

          <!-- 'SuspX&YByPos' 类型下传递 yLine 和 yLineTwo -->
          <chart
            v-if="selectedChartType === 'SuspX&YByPos'"
            :chartId="selectedChartType"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            :yLineTwo="chartDataDefctR"
            :title="'SuspX&YByPos Chart'"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.chart-row {
  margin-bottom: 20px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
  margin-left: 20px;
}
</style>
