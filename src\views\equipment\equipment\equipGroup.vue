<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    @close="props.closeCallBack"
    size="50%"
  >
    <template #header>
      <h4>
        {{
          transformI18n("buttons.hsupdate") +
          ": " +
          transformI18n("equipment.groupName")
        }}
      </h4>
    </template>
    <template #default>
      <div>
        <el-table :data="pageData.equipGroupList" :border="true" row-key="id">
          <el-table-column
            :label="transformI18n('equipment.groupName')"
            prop="groupName"
          >
            <template #default="scope">
              <el-input v-model="scope.row.groupName" v-if="scope.row.isSave" />
              <span v-else>{{ scope.row.groupName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="transformI18n('equipment.description')"
            prop="description"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.description"
                v-if="scope.row.isSave"
              />
              <span v-else>{{ scope.row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            :label="transformI18n('buttons.hsoperations')"
            width="120px"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="deleteById(scope.row.id)"
                >{{ transformI18n("buttons.hsdelete") }}</el-button
              >
              <el-button
                link
                type="primary"
                size="small"
                v-if="!scope.row.isSave"
                @click="toUpdate(scope.row)"
                >{{ transformI18n("buttons.hsupdate") }}</el-button
              ><el-button
                link
                type="primary"
                size="small"
                v-if="scope.row.isSave || false"
                @click="saveEntry(scope.row)"
                >{{ transformI18n("buttons.hssave") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button style="width: 100%" @click="onAddItem()">{{
          transformI18n("buttons.hsadd")
        }}</el-button>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">{{
          transformI18n("buttons.hscancel")
        }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { ElMessageBox } from "element-plus";
import { EquipGroupVo } from "/@/api/model/equipment/equipment_model";
import { transformI18n } from "/@/plugins/i18n";
import { equipGroupAPI } from "/@/api/equipment/equipGroup";

const drawer = ref(true);

onMounted(() => {
  pageData.equipGroupList = props.equipGroupList;
});

const props = defineProps({
  equipGroupList: {
    type: Array<EquipGroupVo>,
    default: null
  },
  closeCallBack: {
    type: Function
  }
});

const pageData = reactive<{ equipGroupList: EquipGroupVo[] }>({
  equipGroupList: []
});

function cancelClick() {
  drawer.value = false;
}

const onAddItem = () => {
  let entry: EquipGroupVo = {
    id: null,
    groupName: null,
    description: null,
    isSave: true
  };
  pageData.equipGroupList.push(entry);
};

const saveEntry = (vo: EquipGroupVo) => {
  equipGroupAPI.save(vo).then((res: string) => {
    if (res !== "fail") {
      vo.isSave = false;
      getGroupList();
    }
  });
};

const getGroupList = () => {
  equipGroupAPI.getList().then((res: EquipGroupVo[] | string) => {
    if (res !== "fail") {
      pageData.equipGroupList = res as EquipGroupVo[];
    }
  });
};

const toUpdate = (vo: EquipGroupVo) => {
  vo.isSave = true;
};

const deleteById = (id: string) => {
  if (!id) {
    return;
  }
  ElMessageBox.confirm("Are you delete batch?")
    .then(() => {
      equipGroupAPI.deleteById(id).then(() => {
        getGroupList();
      });
    })
    .catch(() => {
      // catch error
    });
};
</script>
