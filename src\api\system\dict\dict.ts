import BaseRequest from "../../base";
import { BaseClass } from "../../model/domain";
import { DictCache } from "../../model/system/dict_model";
enum API {
  BASE_URL = "/system/dict",
  getCache = "/cache/get",
  updateCache = "/cache/update",
  deleteEntryUrl = "/deleteEntry",
  saveEntryUrl = "/saveEntry"
}
class DictAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }
  getCache(): Promise<DictCache[]> {
    return this.get<DictCache[]>(API.getCache);
  }
  updateCache(): Promise<string> {
    return this.get<string>(API.updateCache);
  }
  deleteEntryById(id: string): Promise<string> {
    return this.delete(API.deleteEntryUrl + "/" + id, null);
  }
  saveEntry<dto extends BaseClass>(data: dto): Promise<string> {
    return this.post<string>(API.saveEntryUrl, data);
  }
}

export const dictApi = new DictAPI();
