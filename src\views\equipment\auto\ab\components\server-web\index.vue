<script setup lang="ts">
import { onBeforeMount, onUnmounted, ref, reactive } from "vue";
import clientPanel from "./client-panel.vue";
import icsPanel from "./ics-panel.vue";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const appMainDom = ref();
const pageSize = reactive({
  width: 0,
  height: 0
});

onBeforeMount(() => {
  appMainDom.value = document.getElementById("appMain");
  pageSize.width = appMainDom.value.clientWidth;
  pageSize.height = appMainDom.value.clientHeight;
  window.onresize = () => {
    return (() => {
      pageSize.width = appMainDom.value.clientWidth;
      pageSize.height = appMainDom.value.clientHeight;
    })();
  };
});

onUnmounted(() => {
  toggleAlarm(false);
});

const toggleAlarm = (signal: boolean) => {
  if (signal) {
    appMainDom.value.classList.add("warningbg");
  } else {
    appMainDom.value.classList.remove("warningbg");
  }
};
</script>

<template>
  <div>
    <clientPanel :icsEquipInfo="props.icsEquipInfo" />
    <el-divider v-if="props.icsEquipInfo.ics_l" />
    <icsPanel
      v-if="props.icsEquipInfo.ics_l"
      :icsEquipInfo="props.icsEquipInfo.ics_l"
      :pageWidth="pageSize.width"
      :pageHeight="pageSize.height"
    />
    <el-divider v-if="props.icsEquipInfo.ics_r" />
    <icsPanel
      v-if="props.icsEquipInfo.ics_r"
      :icsEquipInfo="props.icsEquipInfo.ics_r"
      :pageWidth="pageSize.width"
      :pageHeight="pageSize.height"
    />
  </div>
</template>

<style scoped></style>
