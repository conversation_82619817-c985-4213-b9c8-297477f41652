<script setup lang="ts">
import { onBeforeMount, onMounted, ref, onUnmounted } from "vue";
import { Stomp } from "@stomp/stompjs";
import acrMap from "./components/acr-map.vue";
import acrInfo from "./components/acr-info.vue";
import acrControl from "./components/acr-control.vue";
import acrOrder from "./components/acr-order.vue";
import { acrAPIs } from "/@/api/acr/acrAPI";
import { useAcrStoreHook } from "/@/store/modules/acr";

defineOptions({
  name: "ControlPanel"
});

const stompClient = Stomp.client(
  `ws://${location.host}/${
    process.env.NODE_ENV === "production" ? "" : "ws/"
  }acr-data`
);

stompClient.onConnect = frame => {
  console.log("Connected: " + frame);
  stompClient.subscribe(`/topic/acr/info/${acrId.value}`, msg => {
    useAcrStoreHook().setAcrInfo(JSON.parse(msg.body));
    acrMapComp.value?.updateMapMarkers();
  });
  stompClient.subscribe(`/topic/dispatcher/status/${areaId}`, msg => {
    useAcrStoreHook().setDispatcherStatus(JSON.parse(msg.body));
  });
  stompClient.subscribe(`/topic/order/last/${acrId.value}`, msg => {
    useAcrStoreHook().setLastOrder(JSON.parse(msg.body));
    acrOrderComp.value?.updateLastOrder();
  });
};

stompClient.onWebSocketError = error => {
  console.error("Error with websocket", error);
  console.error(error);
};

stompClient.onStompError = frame => {
  console.error("Broker reported error: " + frame.headers["message"]);
  console.error("Additional details: " + frame.body);
};

const areaId = "4D-7C";
const acrId = ref();
const acrMapComp = ref();
const acrOrderComp = ref();

onBeforeMount(() => {
  useAcrStoreHook().setAreaId(areaId);
});

onMounted(() => {
  acrAPIs
    .fetchMapMarkers({ areaId })
    .then((res: any) => {
      useAcrStoreHook().setMapMarkers(res);
      acrId.value = res.find(marker => marker.objType == 0).acrId;
      connect();
    })
    .catch(err => {
      console.error(err);
    });
});

onUnmounted(() => {
  stompClient.deactivate();
});

const connect = () => {
  stompClient.activate();
};
</script>

<template>
  <div>
    <el-card :body-style="{ padding: '0px' }">
      <el-row align="middle" style="margin-top: 1rem; margin-left: 1rem">
        <el-col :span="24" align="left">
          <span style="font-weight: bold; font-size: 20px">
            ACR Control Panel
          </span>
        </el-col>
      </el-row>
      <el-divider border-style="none" />
      <!-- <el-row>
        <el-col>
          <connectionStatus />
        </el-col>
      </el-row> -->
      <!-- <el-divider
        content-position="center"
        border-style="none"
        style="margin-bottom: 1rem"
        >Real-Time Map & Information</el-divider
      > -->
      <el-row style="margin-top: 5px">
        <el-col :span="19">
          <acrMap ref="acrMapComp" />
        </el-col>
        <el-col :span="5">
          <acrInfo />
        </el-col>
      </el-row>
      <el-divider
        content-position="center"
        border-style="none"
        style="margin-top: 1.5rem; margin-bottom: 1rem"
        >STATE: {{ useAcrStoreHook().getDispatcherStatus.acrState }}</el-divider
      >
      <el-divider border-style="dashed" style="margin-bottom: 1rem" />
      <el-row>
        <el-col>
          <acrControl />
        </el-col>
      </el-row>
      <el-divider border-style="dashed" style="margin-bottom: 1rem" />
      <el-row align="middle">
        <el-col align="center">
          <acrOrder ref="acrOrderComp" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.el-divider {
  padding: 0;
  margin: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.el-divider__text) {
  font-weight: bold !important;
  font-size: 1rem !important;
}
</style>
