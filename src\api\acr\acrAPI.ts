import BaseRequest from "../base";
enum API {
  BASE_URL = "/acr/control/panel",
  MAP_MARKERS = "/map/markers",
  SWITCH_DISPATCHER_GET = "/switch/dispatcher/get",
  SWITCH_DISPATCHER = "/switch/dispatcher",
  ORDER_LIST = "/order/list",
  ORDER_MOVE = "/order/move",
  ORDER_PAUSE = "/order/pause",
  ORDER_RESUME = "/order/resume",
  ORDER_STOP = "/order/stop",
  ORDER_RESET = "/order/reset",
  CONFIRM_TRAY = "/order/confirm/tray",
  CONFIRM_ASSY = "/order/confirm/assy",
  CLEAR_TRAY_STACKING = "/order/clear/stacking"
}

class AcrAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  fetchMapMarkers<T>(query): Promise<T> {
    return this.get<T>(API.MAP_MARKERS, query);
  }

  fetchOrderList<T>(query): Promise<T> {
    return this.post<T>(API.ORDER_LIST, query);
  }

  getDispatcherSwitch<T>(query): Promise<T> {
    return this.get<T>(API.SWITCH_DISPATCHER_GET, query);
  }

  switchDispatcher<T>(query): Promise<T> {
    return this.post<T>(API.SWITCH_DISPATCHER, query);
  }

  moveOrder<T>(query): Promise<T> {
    return this.post<T>(API.ORDER_MOVE, query);
  }

  pauseOrder<T>(): Promise<T> {
    return this.post<T>(API.ORDER_PAUSE);
  }

  resumeOrder<T>(): Promise<T> {
    return this.post<T>(API.ORDER_RESUME);
  }

  stopOrder<T>(): Promise<T> {
    return this.post<T>(API.ORDER_STOP);
  }

  resetOrder<T>(): Promise<T> {
    return this.post<T>(API.ORDER_RESET);
  }

  confirmTray<T>(query): Promise<T> {
    return this.post<T>(API.CONFIRM_TRAY, query);
  }

  confirmAssy<T>(query): Promise<T> {
    return this.post<T>(API.CONFIRM_ASSY, query);
  }

  clearTrayStacking<T>(query): Promise<T> {
    return this.post<T>(API.CLEAR_TRAY_STACKING, query);
  }
}

export const acrAPIs = new AcrAPI();
