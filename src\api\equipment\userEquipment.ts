import BaseRequest from "../base";
enum API {
  BASE_URL = "/equipment/userEquipment"
}

class UserEquipmentAPI extends BaseRequest {
  getJarIdUrl: string;

  constructor() {
    super();
    this.getJarIdUrl = "/getJarIdList";
  }

  EquipmentType: Array<{ key: string; value: string }> = [
    { key: "1", value: "拉上终端" },
    { key: "2", value: "图片读取终端" },
    { key: "3", value: "TXT文本读取终端" },
    { key: "4", value: "基恩士" },
    { key: "5", value: "ICS-TXT文本读取终端" },
    { key: "6", value: "文件共享" }
  ];

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getJarIdList<T>(): Promise<T> {
    return this.get<T>(this.getJarIdUrl);
  }
}

export const userEquipmentAPI = new UserEquipmentAPI();
