<script setup lang="ts">
import { onBeforeMount, reactive, ref } from "vue";
import { XJSBB } from "/@/api/xjsbb/xjsbb";
import Chart from "./chart.vue"; // Import chart component
import AVG_Chart from "./avg_chart.vue"; // Import chart component
import dayjs from "dayjs";
// Reactive variables for storing different chart data
const chartDataDefctL = ref<any[]>([]);

const xAxisDataL = ref<string[]>([]);

const AVG_xAxisData = ref<string[]>([]);
const AVG_chartDataDefct = ref<number[]>([]);

// 用于存储选中的日期
const selectedDate = ref();

const pageData = reactive({
  groupList: [],
  searchInfo: {
    groupId: ""
  }
});

// "LeftHGA PAD0 BondPosX": "LeftHGA PAD0 BondPosX",
// "LeftHGA PAD0 BondPosY": "LeftHGA PAD0 BondPosY",
// "LeftHGA BondPosZ": "LeftHGA BondPosZ",
// "LeftHGA A-Dim": "LeftHGA A-Gap",
// "LeftHGA B-Dim": "LeftHGA B-Gap",
// "RightHGA PAD0 BondPosX": "RightHGA PAD0 BondPosX",
// "RightHGA PAD0 BondPosY": "RightHGA PAD0 BondPosY",
// "RightHGA BondPosZ": "RightHGA BondPosZ",
// "RightHGA A-Dim": "RightHGA A-Gap",
// "RightHGA B-Dim": "RightHGA B-Gap"
const chartOptions = {
  "LeftHGA BondTopX": "LeftHGA BondTopX",
  "LeftHGA BondTopY": "LeftHGA BondTopY",
  "LeftHGA BondSideX": "LeftHGA BondSideX",
  "LeftHGA BondSideZ": "LeftHGA BondSideZ",
  "LeftHGA A-Dim": "LeftHGA A-Gap",
  "LeftHGA B-Dim": "LeftHGA B-Gap",
  "RightHGA BondTopX": "RightHGA BondTopX",
  "RightHGA BondTopY": "RightHGA BondTopY",
  "RightHGA BondSideX": "RightHGA BondSideX",
  "RightHGA BondSideZ": "RightHGA BondSideZ",
  "RightHGA A-Dim": "RightHGA A-Gap",
  "RightHGA B-Dim": "RightHGA B-Gap"
};

// Selected chart type from dropdown
const selectedChartType = ref<string>("");

// Get data function, passing time range as parameters
const fetchData = async (date: string, type: string, groupId: string) => {
  XJSBB.getTray({ selected: date, paramName: type, groupId }).then(
    (res: any) => {
      if (res !== "fail") {
        processIVSChartData(res);
      }
    }
  );
};

// Data processing functions
const processIVSChartData = (res: any) => {
  xAxisDataL.value = getxAxisData(res);
  chartDataDefctL.value = getSeriesFromResData(res);
  AVG_xAxisData.value = getxAxisData(res);
  Object.keys(res).forEach(key => {
    if (key.includes("AVG")) {
      AVG_chartDataDefct.value = res[key] || [];
    }
  });
};
const getxAxisData = (xAxis: any) => {
  return xAxis["xAxisData"];
};

// Helper function for chart data formatting
const getSeriesFromResData = (series: any) => {
  const seriesData: any[] = [];
  Object.keys(series).forEach(key => {
    if (key === "xAxisData" || key.includes("AVG")) {
      return;
    }
    seriesData.push({
      name: key,
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: series[key] || []
    });
  });
  return seriesData;
};

// 日期选择限制，禁用今天以后的日期
const disabledDate = (date: Date) => {
  return date > new Date();
};
// 监听日期选择变化事件
const onDateChange = (date: string) => {
  selectedDate.value = date;
  fetchData(date, selectedChartType.value, pageData.searchInfo.groupId); // 当选择日期时，重新获取数据
};

// Handle dropdown selection changes
const onChartTypeChange = (type: string) => {
  selectedChartType.value = type;
  fetchData(selectedDate.value, type, pageData.searchInfo.groupId); // Fetch data for other chart types
};

onBeforeMount(() => {
  const today = [dayjs().format("YYYY-MM-DD")]; // 获取今天的日期
  selectedDate.value = today;
  selectedChartType.value = "LeftHGA BondTopX"; // 默认选中第一个图表类型
  XJSBB.getList().then((res: any) => {
    pageData.groupList = res;
    pageData.searchInfo.groupId = pageData.groupList[0].groupId;
    fetchData(
      selectedDate.value,
      selectedChartType.value,
      pageData.searchInfo.groupId
    );
  });
});

const reloadByGroup = () => {};
</script>

<template>
  <div>
    <div>
      <el-card class="query-card">
        <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
          <el-form-item label="Line ID">
            <el-select
              v-model="pageData.searchInfo.groupId"
              placeholder="Line ID"
              @change="reloadByGroup"
            >
              <el-option
                v-for="item in pageData.groupList"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <el-form-item label="选择日期">
              <el-date-picker
                v-model="selectedDate"
                type="dates"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                @change="onDateChange"
                class="date-picker"
              />
            </el-form-item>

            <el-form-item label="选择类型">
              <el-select
                v-model="selectedChartType"
                placeholder="请选择"
                @change="onChartTypeChange"
              >
                <el-option
                  v-for="(label, key) in chartOptions"
                  :key="key"
                  :label="label"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <!-- 只在 selectedChartType 不为 'BFSDX&AFSDY', 'SuspX&YByPos', 和 'BFSDX&BFSDY' 时展示 chart 组件 -->
          <AVG_Chart
            class="chart-row"
            :chartId="'AVG' + selectedChartType"
            :xLine="AVG_xAxisData"
            :yLine="AVG_chartDataDefct"
            :title="'AVG' + chartOptions[selectedChartType]"
          />

          <chart
            :chartId="selectedChartType"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            :title="chartOptions[selectedChartType]"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.chart-row {
  margin-bottom: 20px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
  margin-left: 20px;
}
</style>
