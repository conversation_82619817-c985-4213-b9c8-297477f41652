import BaseRequest from "../base";
enum API {
  BASE_URL = "/system/user",
  TOGGLE_USER = "/toggle",
  SET_ROLE = "/setrole"
}

class UserAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }
  toggleUser(userId): any {
    return this.put(API.TOGGLE_USER + "/" + userId, null);
  }
  setRole(userId, roleId): any {
    return this.put(API.SET_ROLE + "/" + userId + "/" + roleId, null);
  }
}

export const userAPI = new UserAPI();
