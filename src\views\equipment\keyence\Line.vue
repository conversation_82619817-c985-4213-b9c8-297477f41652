<script setup lang="ts">
import { onMounted, reactive } from "vue";
import * as echarts from "echarts";
import {
  KeyenceQuery,
  LineChartVO
} from "/@/api/model/equipment/keyence_model";
import { keyenceAPI } from "/@/api/equipment/keyence";
type EChartsOption = echarts.EChartsOption;

const init = (xLine: Array<string>, yLine: Array<string>) => {
  var chartDom = document.getElementById("main")!;
  var myChart = echarts.init(chartDom);
  var option: EChartsOption;
  option = {
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: xLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis"
    },
    legend: {},
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xLine
    },
    yAxis: {
      type: "value",
      scale: true,
      axisLabel: {
        formatter: "{value}"
      }
    },
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 10,
        borderColor: "rgba(0,0,0,0)",
        textStyle: {
          color: "#05D5FF"
        }
      }
    ],
    series: [
      {
        type: "line",
        data: yLine,
        markPoint: {
          data: [
            { type: "max", name: "Max" },
            { type: "min", name: "Min" }
          ]
        },
        markLine: {
          data: [{ type: "average", name: "Avg" }]
        }
      }
    ]
  };

  option && myChart.setOption(option);
};

const props = defineProps({
  txt: {
    type: Object as PropType<KeyenceQuery>,
    required: true
  }
});

const pageData = reactive<{
  queryVo: KeyenceQuery;
}>({
  queryVo: null
});

const chartData = reactive<{
  xLine: string[];
  yLine: string[];
}>({
  xLine: [],
  yLine: []
});

onMounted(() => {
  pageData.queryVo = props.txt;
  if (pageData.queryVo.paramId == null) {
    return;
  }
  let startHour = props.txt.startTime.split(":")[0];
  let endHour = props.txt.endTime.split(":")[0];
  if (parseInt(endHour) - parseInt(startHour) > 4) {
    init(chartData.xLine, chartData.yLine);
    return;
  }
  let xLine: Array<string>;
  let yLine: Array<string>;
  keyenceAPI
    .getKeyenceLineChart(props.txt)
    .then((res: LineChartVO | string) => {
      if (res !== "fail" && res !== null) {
        xLine = (res as LineChartVO).xLine;
        yLine = (res as LineChartVO).yLine;

        chartData.xLine = xLine;
        chartData.yLine = yLine;
        init(xLine, yLine);
      } else {
        init(chartData.xLine, chartData.yLine);
      }
    });
});
</script>

<template>
  <div id="main" style="width: 100%; height: 400px" />
</template>

<style scoped></style>
