/**
 * 响应结构
 */
export interface Result<T> {
  code: string;
  msg: string;
  data: T;
  time: string;
}
export interface BaseClass {
  /**
   * 创建时间
   */
  createdTime?: string;

  createdBy?: string;

  /**
   * 修改时间
   */
  updatedTime?: string;

  updatedBy?: string;

  /**
   * 是否启用
   */
  state?: number;
}
/**
 * page结构体
 */
export interface Page<T> {
  total: number;
  size: number;
  pages: number;
  records: T;
}

export interface BaseQuery {
  sortType?: string;
  sortColumn?: string[];
  groupColumn?: string[];
  pageNum?: number;
  pageSize?: number;
  total?: number;
}

export interface EntityType {
  name?: string;
  value?: string;
}
