<template>
  <div class="equipment-panel">
    <!-- 按钮和统计信息布局 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <button
          @click="toggleDisplayOffline"
          class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
        >
          {{ displayOffline === "0" ? "Select Offline" : "Select All" }}
        </button>
        <!-- 新增统计信息 -->
        <div class="ml-4 flex items-center">
          <div class="status-count online-count" v-if="displayOffline === '0'">
            <span class="count-dot online" />
            <span class="font-medium">Online: {{ onlineCount }}</span>
          </div>
          <div class="status-count offline-count ml-4">
            <span class="count-dot offline" />
            <span class="font-medium">Offline: {{ offlineCount }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-2 gap-4">
      <template v-for="(deviceData, deviceId) in deviceStatus" :key="deviceId">
        <div class="area-section bg-white p-4 rounded shadow">
          <div
            class="area-title text-lg font-bold text-blue-500 mb-4 border-l-4 border-blue-500 pl-4"
          >
            {{ deviceId }}
          </div>
          <div class="area-devices">
            <el-descriptions
              border
              size="small"
              :column="5"
              direction="vertical"
              style="margin-top: 10px"
            >
              <template v-if="deviceData['HDS Client'] !== undefined">
                <el-descriptions-item
                  label="HDS Client"
                  label-align="center"
                  align="center"
                  width="20%"
                >
                  <el-badge
                    :value="
                      deviceData['HDS Client'] === '1' ? 'Online' : 'OffLine'
                    "
                    :type="
                      deviceData['HDS Client'] === '1' ? 'success' : 'danger'
                    "
                  >
                    <IconifyIconOffline
                      class="equip-icon mx-auto"
                      icon="platform"
                    />
                  </el-badge>
                </el-descriptions-item>
              </template>
              <template v-if="deviceData['PLC'] !== undefined">
                <el-descriptions-item
                  label="PLC"
                  label-align="center"
                  align="center"
                  width="20%"
                >
                  <el-badge
                    :value="deviceData['PLC'] === '1' ? 'Online' : 'OffLine'"
                    :type="deviceData['PLC'] === '1' ? 'success' : 'danger'"
                  >
                    <IconifyIconOffline
                      class="equip-icon mx-auto"
                      icon="card"
                    />
                  </el-badge>
                </el-descriptions-item>
              </template>
              <template v-if="deviceData['IVS'] !== undefined">
                <el-descriptions-item
                  label="IVS"
                  label-align="center"
                  align="center"
                  width="20%"
                >
                  <el-badge
                    :value="deviceData['IVS'] === '1' ? 'Online' : 'OffLine'"
                    :type="deviceData['IVS'] === '1' ? 'success' : 'danger'"
                  >
                    <IconifyIconOffline
                      class="equip-icon mx-auto"
                      icon="monitor"
                    />
                  </el-badge>
                </el-descriptions-item>
              </template>
              <template v-if="deviceData['ICS-L'] !== undefined">
                <el-descriptions-item
                  label="ICS-L"
                  label-align="center"
                  align="center"
                  width="20%"
                >
                  <el-badge
                    :value="deviceData['ICS-L'] === '1' ? 'Online' : 'OffLine'"
                    :type="deviceData['ICS-L'] === '1' ? 'success' : 'danger'"
                  >
                    <IconifyIconOffline
                      class="equip-icon mx-auto"
                      icon="monitor"
                    />
                  </el-badge>
                </el-descriptions-item>
              </template>
              <template v-if="deviceData['ICS-R'] !== undefined">
                <el-descriptions-item
                  label="ICS-R"
                  label-align="center"
                  align="center"
                  width="20%"
                >
                  <el-badge
                    :value="deviceData['ICS-R'] === '1' ? 'Online' : 'OffLine'"
                    :type="deviceData['ICS-R'] === '1' ? 'success' : 'danger'"
                  >
                    <IconifyIconOffline
                      class="equip-icon mx-auto"
                      icon="monitor"
                    />
                  </el-badge>
                </el-descriptions-item>
              </template>
            </el-descriptions>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { equipGroupAPI } from "/@/api/equipment/equipStatus";
import { onMounted, ref, onBeforeUnmount, computed } from "vue";

let timer: any = null;
const deviceStatus = ref({});
const displayOffline = ref("0");

// 计算在线设备数量
const onlineCount = computed(() => {
  let count = 0;
  Object.values(deviceStatus.value).forEach((devices: any) => {
    Object.values(devices).forEach((status: any) => {
      if (status === "1") {
        count++;
      }
    });
  });
  return count;
});

// 计算离线设备数量
const offlineCount = computed(() => {
  let count = 0;
  Object.values(deviceStatus.value).forEach((devices: any) => {
    Object.values(devices).forEach((status: any) => {
      if (status === "0") {
        count++;
      }
    });
  });
  return count;
});

const fetchStatus = async () => {
  try {
    const res = await equipGroupAPI.getList({
      displayOffLine: displayOffline.value
    });
    if (res !== "fail") {
      deviceStatus.value = res;
    }
  } catch (error) {
    console.error("Failed to fetch device status:", error);
  }
};

const toggleDisplayOffline = () => {
  displayOffline.value = displayOffline.value === "0" ? "1" : "0";
  fetchStatus();
};

onMounted(() => {
  fetchStatus();
  timer = setInterval(() => {
    fetchStatus();
  }, 30000);
});

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style scoped>
.equipment-panel {
  padding: 20px;
}

.el-badge {
  width: 40%;
  height: 40%;
  margin-top: 15%;
  margin-right: 25%;
}

.equip-icon {
  font-size: 12px;
  width: 40px;
  height: 40px;
  display: block;
}

/* 统计信息样式 */
.status-count {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.count-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.online {
  background-color: #67c23a;
}

.offline {
  background-color: #f56c6c;
}

/* 自定义描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: bold;
}

:deep(.el-descriptions__content) {
  padding: 8px 10px;
}
</style>
