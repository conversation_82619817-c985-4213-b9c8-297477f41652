<script setup lang="ts">
import { onMounted, reactive } from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { transformI18n } from "/@/plugins/i18n";
import { harmAPI } from "/@/api/harm/harm"; // 请确认实际API路径

const pageData = reactive({
  loading: false,
  dataList: [],
  dateRange: [],
  searchInfo: {
    headSn: "",
    trayNo: "",
    tester: "",
    projCode: "",
    startTime: "",
    endTime: "",
    pageSize: 10,
    pageNum: 1,
    total: 0,
    sortColumn: [],
    sortType: ""
  }
});

onMounted(() => {
  pageData.dateRange = [
    dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    dayjs().format("YYYY-MM-DD HH:mm:ss")
  ];
  loadPage();
});

const loadPage = () => {
  pageData.searchInfo.startTime = pageData.dateRange[0];
  pageData.searchInfo.endTime = pageData.dateRange[1];
  getPage();
};

const dateRangeChange = (dateRange: [string, string]) => {
  pageData.dateRange = dateRange;
  getPage();
};

const getPage = () => {
  // pageData.dataList = [
  //   {
  //     HEAD_SN: "1",
  //     TEST_TIME: "2021-09-01 00:00:00",
  //     TRAY_NO: "1",
  //     TESTER: "1",
  //     PROJ_CODE: "1",
  //     POSITION: "1",
  //     BAG_ID: "1",
  //     OCR_POSI: "1",
  //     LA_OR_LB: "1",
  //     Susp_height_data: "1",
  //     Susp_flexure_hole_image: "1",
  //     GLUE_SHOT_X01: "1",
  //     GLUE_SHOT_Y01: "1",
  //     GLUE_SHOT_TIME01: "1",
  //     GLUE_SHOT_X02: "1",
  //     GLUE_SHOT_Y02: "1",
  //     GLUE_SHOT_TIME02: "1",
  //     GLUE_SHOT_X03: "1",
  //     GLUE_SHOT_Y03: "1",
  //     GLUE_SHOT_TIME03: "1",
  //     Pre_UV_time: "1",
  //     Susp_T_hole_ICS_image: "1",
  //     EX1: "1",
  //     EX2: "1",
  //     EX3: "1",
  //     EY1: "1",
  //     EY2: "1",
  //     EY3: "1",
  //     ED1: "1",
  //     ED2: "1",
  //     ED3: "1",
  //     Pre_curing_UV_time: "1",
  //     Pre_curing_Laser_time: "1",
  //     Slider_image: "1",
  //     LD_LDU_image: "1",
  //     SMALL_ADIM_OFFSET01: "1",
  //     SMALL_BDIM_OFFSET01: "1",
  //     SMALL_Angle_OFFSET01: "1",
  //     AB_dim_image: "1",
  //     A_dim: "1",
  //     B_dim: "1"
  //   }
  // ];
  // pageData.searchInfo.total = 1;
  pageData.loading = true;
  harmAPI
    .page(pageData.searchInfo)
    .then((res: any) => {
      if (res.code === 200) {
        pageData.dataList = res.data.records;
        pageData.searchInfo.total = res.data.total;
      }
    })
    .finally(() => (pageData.loading = false));
};

const sizeChange = (pageSize: number) => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const onSearch = () => {
  getPage();
};

const resetSearch = () => {
  pageData.searchInfo.headSn = "";
  pageData.searchInfo.trayNo = "";
  pageData.searchInfo.tester = "";
  pageData.searchInfo.projCode = "";
  pageData.dateRange = [
    dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    dayjs().format("YYYY-MM-DD HH:mm:ss")
  ];
};

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const handleSortChange = ({ prop, order }: any) => {
  pageData.searchInfo.sortColumn = prop ? [prop] : [];
  pageData.searchInfo.sortType = order === "ascending" ? "ASC" : "DESC";
  getPage();
};

const exportData = () => {
  pageData.loading = true;
  const exportParams = {
    ...pageData.searchInfo,
    startTime: pageData.dateRange[0],
    endTime: pageData.dateRange[1]
  };

  harmAPI
    .download(exportParams)
    .then((res: any) => {
      if (res.code === 200) {
        // 创建隐藏的可下载链接
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute(
          "download",
          `test_data_${dayjs().format("YYYYMMDDHHmmss")}.xlsx`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    })
    .finally(() => (pageData.loading = false));
};
</script>

<template>
  <el-card>
    <el-card class="query-card">
      <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
        <!-- 时间范围 -->
        <el-form-item label="Test Time">
          <el-date-picker
            v-model="pageData.dateRange"
            type="datetimerange"
            range-separator="To"
            start-placeholder="Start time"
            end-placeholder="End time"
            :disabled-date="disabledDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="dateRangeChange"
          />
        </el-form-item>

        <!-- 搜索字段 -->
        <el-form-item label="HEAD_SN">
          <el-input
            v-model="pageData.searchInfo.headSn"
            placeholder="Input HEAD_SN"
            clearable
          />
        </el-form-item>

        <el-form-item label="TRAY_NO">
          <el-input
            v-model="pageData.searchInfo.trayNo"
            placeholder="Input TRAY_NO"
            clearable
          />
        </el-form-item>

        <el-form-item label="PROJ_CODE">
          <el-input
            v-model="pageData.searchInfo.projCode"
            placeholder="Input PROJ_CODE"
            clearable
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            :loading="pageData.loading"
            @click="onSearch"
          >
            {{ transformI18n("query.search") }}
          </el-button>
          <el-button
            type="success"
            :icon="useRenderIcon('download')"
            :loading="pageData.loading"
            @click="exportData"
          >
            {{ transformI18n("query.export") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('refresh')"
            :loading="pageData.loading"
            @click="resetSearch"
          >
            {{ transformI18n("query.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 表格结构修正 -->
    <el-table
      v-loading="pageData.loading"
      :data="pageData.dataList"
      border
      @sort-change="handleSortChange"
    >
      <!-- 固定前九列 -->
      <el-table-column prop="HEAD_SN" fixed="left" label="HEAD_SN" />
      <el-table-column prop="TEST_TIME" fixed="left" label="TEST_TIME" />
      <el-table-column prop="TRAY_NO" fixed="left" label="TRAY_NO" />
      <el-table-column prop="TESTER" fixed="left" label="TESTER" />
      <el-table-column prop="PROJ_CODE" fixed="left" label="PROJ_CODE" />
      <el-table-column prop="POSITION" fixed="left" label="POSITION" />
      <el-table-column prop="BAG_ID" fixed="left" label="BAG_ID" />
      <el-table-column prop="OCR_POSI" fixed="left" label="OCR_POSI" />
      <el-table-column prop="LA_OR_LB" fixed="left" label="LA OR LB" />

      <el-table-column prop="Susp_height_data" label="Susp_height data" />
      <el-table-column
        prop="Susp_flexure_hole_image"
        label="Susp_flexure hole image"
        :show-overflow-tooltip="true"
      />

      <!-- GLUE_SHOT 系列 -->
      <el-table-column prop="GLUE_SHOT_X01" label="GLUE_SHOT_X01" />
      <el-table-column prop="GLUE_SHOT_Y01" label="GLUE_SHOT_Y01" />
      <el-table-column prop="GLUE_SHOT_TIME01" label="GLUE_SHOT_TIME 01" />
      <el-table-column prop="GLUE_SHOT_X02" label="GLUE_SHOT_X02" />
      <el-table-column prop="GLUE_SHOT_Y02" label="GLUE_SHOT_Y02" />
      <el-table-column prop="GLUE_SHOT_TIME02" label="GLUE_SHOT_TIME 02" />
      <el-table-column prop="GLUE_SHOT_X03" label="GLUE_SHOT_X03" />
      <el-table-column prop="GLUE_SHOT_Y03" label="GLUE_SHOT_Y03" />
      <el-table-column prop="GLUE_SHOT_TIME03" label="GLUE_SHOT_TIME 03" />

      <!-- Pre_UV 相关 -->
      <el-table-column prop="Pre_UV_time" label="Pre_UV time" />

      <!-- 图像识别字段 -->
      <el-table-column
        prop="Susp_T_hole_ICS_image"
        label="Susp_T hole&ICS image"
        :show-overflow-tooltip="true"
      />

      <!-- EX/EY/ED 系列 -->
      <el-table-column prop="EX1" label="EX1" />
      <el-table-column prop="EX2" label="EX2" />
      <el-table-column prop="EX3" label="EX3" />
      <el-table-column prop="EY1" label="EY1" />
      <el-table-column prop="EY2" label="EY2" />
      <el-table-column prop="EY3" label="EY3" />
      <el-table-column prop="ED1" label="ED1" />
      <el-table-column prop="ED2" label="ED2" />
      <el-table-column prop="ED3" label="ED3" />

      <!-- 固化时间 -->
      <el-table-column prop="Pre_curing_UV_time" label="Pre_curing UV_time" />
      <el-table-column
        prop="Pre_curing_Laser_time"
        label="Pre_curing Laser_time"
      />

      <!-- 图像字段 -->
      <el-table-column
        prop="Slider_image"
        label="Slider image"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="LD_LDU_image"
        label="LD&LDU image"
        :show-overflow-tooltip="true"
      />

      <!-- 偏移量 -->
      <el-table-column
        prop="SMALL_ADIM_OFFSET01"
        label="SMALL_ADIM_OFFSET 01"
      />
      <el-table-column
        prop="SMALL_BDIM_OFFSET01"
        label="SMALL_BDIM_OFFSET 01"
      />
      <el-table-column
        prop="SMALL_Angle_OFFSET01"
        label="SMALL_Angle_OFFSET 01"
      />

      <!-- AB dim -->
      <el-table-column
        prop="AB_dim_image"
        label="AB dim image"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="a_dim" label="A-dim" />
      <el-table-column prop="b_dim" label="B-dim" />
    </el-table>

    <el-pagination
      v-model:currentPage="pageData.searchInfo.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageData.searchInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageData.searchInfo.total"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </el-card>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
}

.table-title {
  font-weight: bold;
  font-size: 20px;
}

.el-form-item {
  margin-bottom: 12px;
}

.el-input {
  width: 200px;
}

.el-date-editor {
  width: 400px;
}
</style>
