<script setup lang="ts">
import { onMounted, reactive, watch, ref } from "vue";
import * as echarts from "echarts";
import { ocrIcsTxtAPI } from "/@/api/equipment/ocrIcsTxt";
import {
  paramList,
  FileVO,
  OcrIcsChartVO2,
  seriesDataVO,
  getFileListParam
} from "/@/api/model/equipment/ocr_ics_txt_model";
import ViewImage from "./ViewImage.vue";
import { errorMessage } from "/@/utils/message";

type EChartsOption = echarts.EChartsOption;

let yAxisMin = 0;
let yAxisMax = 0;

const init = (xLine: Array<string>, yLine: Array<object>) => {
  let myChart = echarts.init(document.getElementById("main2"));
  rightClickMenu.chart = myChart;
  let option: EChartsOption;

  option = {
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {},
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: xLine
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        min: yAxisMin == 0 ? null : yAxisMin,
        max: yAxisMax == 0 ? null : yAxisMax
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 10,
        borderColor: "rgba(0,0,0,0)",
        textStyle: {
          color: "#05D5FF"
        }
      }
    ],
    series: yLine
  };
  option && myChart.setOption(option);
};

const pageData = reactive<{
  searchTestInfo: paramList;
}>({
  searchTestInfo: {
    testTime: null,
    tester: null,
    position: null,
    paramType: "ED1",
    groupId: null
  }
});

const rightClickMenu = reactive<{
  visible: boolean;
  top: number;
  left: number;
  chart: any;
  time: string;
  fileVoArr: FileVO[];
}>({
  visible: false,
  top: 0,
  left: 0,
  chart: null,
  time: "",
  fileVoArr: []
});

const viewimage = ref();

defineOptions({
  name: "icsTxtStatisticLine"
});

const props = defineProps({
  txt: {
    type: Object as PropType<paramList>,
    required: true
  }
});

onMounted(() => {
  pageData.searchTestInfo = props.txt;
  getData();
});

const getData = () => {
  if (
    pageData.searchTestInfo == null ||
    pageData.searchTestInfo.testTime == null ||
    pageData.searchTestInfo.tester == null
  ) {
    return;
  }
  ocrIcsTxtAPI
    .getChartData2(pageData.searchTestInfo)
    .then((res: OcrIcsChartVO2 | string) => {
      if (res !== "fail" && res !== null) {
        const ocrIcsTxtVO = res as OcrIcsChartVO2;
        let xAxisData = ocrIcsTxtVO.xLine;
        let seriesData = [];

        seriesData.push(getSeriesDataVO("P1", ocrIcsTxtVO.pos1));
        seriesData.push(getSeriesDataVO("P2", ocrIcsTxtVO.pos2));
        seriesData.push(getSeriesDataVO("P3", ocrIcsTxtVO.pos3));
        seriesData.push(getSeriesDataVO("P4", ocrIcsTxtVO.pos4));
        seriesData.push(getSeriesDataVO("P5", ocrIcsTxtVO.pos5));
        seriesData.push(getSeriesDataVO("P6", ocrIcsTxtVO.pos6));
        seriesData.push(getSeriesDataVO("P7", ocrIcsTxtVO.pos7));
        seriesData.push(getSeriesDataVO("P8", ocrIcsTxtVO.pos8));
        seriesData.push(getSeriesDataVO("P9", ocrIcsTxtVO.pos9));
        seriesData.push(getSeriesDataVO("P10", ocrIcsTxtVO.pos10));

        let fullData = [];
        fullData.push(...ocrIcsTxtVO.pos1);
        fullData.push(...ocrIcsTxtVO.pos2);
        fullData.push(...ocrIcsTxtVO.pos3);
        fullData.push(...ocrIcsTxtVO.pos4);
        fullData.push(...ocrIcsTxtVO.pos5);
        fullData.push(...ocrIcsTxtVO.pos6);
        fullData.push(...ocrIcsTxtVO.pos7);
        fullData.push(...ocrIcsTxtVO.pos8);
        fullData.push(...ocrIcsTxtVO.pos9);
        fullData.push(...ocrIcsTxtVO.pos10);

        yAxisMax = Math.max(...fullData);
        yAxisMin = Math.min(...fullData);

        let avgSpecLine = getSeriesDataVO("avg", ocrIcsTxtVO.average);
        let markLineData: any = {
          symbol: ["none", "none"],
          data: []
        };

        let spec = ocrIcsTxtVO.spec;
        if (spec !== undefined) {
          let uslVal = spec["USL"];
          let usl = {
            name: "USL",
            yAxis: parseInt(uslVal),
            lineStyle: {
              color: "red",
              type: "solid",
              width: 2
            },
            label: {
              formatter: "USL=" + uslVal,
              fontSize: 12,
              fontWeight: "bolder",
              color: "red",
              position: "middle"
            },
            zlevel: 5
          };
          yAxisMax = parseInt(uslVal) > yAxisMax ? parseInt(uslVal) : yAxisMax;
          markLineData.data.push(usl);
          let lslVal = spec["LSL"];
          let lsl = {
            name: "LSL",
            yAxis: parseInt(lslVal),
            lineStyle: {
              color: "red",
              type: "solid",
              width: 2
            },
            label: {
              formatter: "LSL=" + lslVal,
              fontSize: 12,
              fontWeight: "bolder",
              color: "red",
              position: "middle"
            },
            zlevel: 5
          };
          yAxisMin = parseInt(lslVal) < yAxisMin ? parseInt(lslVal) : yAxisMin;
          markLineData.data.push(lsl);
          avgSpecLine.markLine = markLineData;
        }
        seriesData.push(avgSpecLine);

        init(xAxisData, seriesData);
      } else {
        init([], []);
      }
    });
};

const getSeriesDataVO = (name: string, data: Array<number>) => {
  var svo: seriesDataVO = {
    name: name,
    type: "line",
    stack: name,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null,
    data: data
  };
  return svo;
};

// 从鼠标事件监听右键菜单可见值更新
watch(
  () => rightClickMenu.visible,
  _newVisible => {
    _newVisible
      ? document.body.addEventListener("click", closeMenu)
      : document.body.removeEventListener("click", closeMenu);
  }
);

const openMenu = e => {
  // 鼠标相对坐标
  let x = e.clientX;
  let y = e.clientY;
  // 菜单相对鼠标位置，设置菜单可见
  rightClickMenu.left = x - 95;
  rightClickMenu.top = y - 22;
  rightClickMenu.visible = true;
  // 鼠标相对于echart的坐标转换为echart逻辑坐标,获取横坐标X
  if (rightClickMenu.chart !== null) {
    let pointInGrid = rightClickMenu.chart.convertFromPixel(
      { seriesIndex: 0 },
      [e.offsetX, e.offsetY]
    );
    // 根据echart坐标获取对应值
    rightClickMenu.time =
      rightClickMenu.chart.getOption().xAxis[0].data[pointInGrid[0]];
  }
};

const closeMenu = () => {
  rightClickMenu.visible = false;
};

// 弹出图片查看组件
const openImageView = () => {
  let query: getFileListParam = {
    equipmentId: pageData.searchTestInfo.tester,
    date: pageData.searchTestInfo.testTime,
    time: rightClickMenu.time
  };
  if (query.time === null || query.time === "") {
    errorMessage("Empty Chart");
    return;
  }
  ocrIcsTxtAPI.getFileListByTime(query).then((res: FileVO[] | string) => {
    if (res !== "fail" && res.length > 0) {
      let fileArr = res as FileVO[];
      rightClickMenu.fileVoArr = fileArr;
      doOpen();
    } else {
      errorMessage("No Image Found");
    }
  });
};

const doOpen = () => {
  viewimage.value.setVisible();
};
</script>

<template>
  <div>
    <div
      id="main2"
      style="width: 100%; height: 400px"
      @contextmenu.prevent="openMenu($event)"
    />
    <div>
      <ul
        v-show="rightClickMenu.visible"
        :style="{
          left: rightClickMenu.left + 'px',
          top: rightClickMenu.top + 'px'
        }"
        class="contextmenu"
      >
        <li @click="openImageView">View Image</li>
      </ul>
      <view-image :file-list="rightClickMenu.fileVoArr" ref="viewimage" />
    </div>
  </div>
</template>

<style scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  position: fixed;
  z-index: 3000;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}

.contextmenu li:hover {
  background: #eee;
}
</style>
