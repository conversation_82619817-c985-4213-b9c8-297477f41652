<script setup lang="ts">
import { onMounted, ref, reactive, onUnmounted, watch, nextTick } from "vue";
import * as PIXI from "pixijs";
// @ts-ignore
import acrPng from "/@/assets/acrmap/acr.png";
import { useAcrStoreHook } from "/@/store/modules/acr";

defineOptions({
  name: "AcrMap"
});

const width = ref(window.innerWidth);
const height = ref(window.innerHeight);
watch([width, height], () => {
  resizeCanvas();
});

const app = ref();
const mapContainer = ref();
// In pixels
const canvasArea = reactive({
  width: 0,
  height: 0
});
const mapElementsContainer = ref();
const meterToPixelScale = reactive({
  x: 1,
  y: 1
});
const maxMapArea = reactive({
  x: 0,
  y: 0
});
const inited = ref(0);

onMounted(async () => {
  await nextTick();

  let intervalId = setInterval(() => {
    let mapMarkers = useAcrStoreHook().getMapMarkers;
    if (mapMarkers.length > 0) {
      clearInterval(intervalId);

      maxMapArea.x = Math.max(
        ...mapMarkers.map(marker => parseFloat(marker.xcoordinate))
      );
      maxMapArea.y = Math.max(
        ...mapMarkers.map(marker => parseFloat(marker.ycoordinate))
      );
      if (!app.value) {
        initMap(maxMapArea.x, maxMapArea.y);
        addMapMarkers(mapMarkers);
      }
    }
  }, 100); // Check every 100ms

  window.addEventListener("resize", resizeCanvas);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeCanvas);
  if (app.value) {
    app.value.destroy(true, {
      children: true,
      texture: true,
      baseTexture: true
    });
    app.value = null;
  }
});

const calculateCanvasArea = () => {
  canvasArea.width = mapContainer.value.clientWidth - 1;
  canvasArea.height = mapContainer.value.clientWidth * 0.5;
};

const resizeCanvas = () => {
  calculateCanvasArea();
  if (app.value) {
    app.value.renderer.resize(canvasArea.width, canvasArea.height);
    calMapScaler(maxMapArea.x, maxMapArea.y);
    updateMapMarkers();
  }
};

const coordPixelShift = reactive({
  x: 0,
  y: 0
});

// 计算画布与真实距离的缩放比例
const calMapScaler = (maxX, maxY) => {
  // 边缘加至少1m的边界范围, 超过则加5%的边界范围
  let addedXVal = maxX * 0.05 < 2 ? 2 : maxX * 0.05;
  let addedYVal = maxY * 0.05 < 2 ? 2 : maxY * 0.05;
  maxX += addedXVal * 2;
  maxY += addedYVal * 2;
  // 画布米到像素转换比
  meterToPixelScale.x = canvasArea.width / maxX;
  meterToPixelScale.y = canvasArea.height / maxY;
  // 画布物体相对位置偏移量, 如(0,0)加上偏移量后为(90, 80)表示起点
  coordPixelShift.x = meterToPixelScale.x * addedXVal;
  coordPixelShift.y = meterToPixelScale.y * addedYVal;
};

const convertMetersToPixels = (x, y) => {
  x = x * meterToPixelScale.x + coordPixelShift.x;
  y = y * meterToPixelScale.y + coordPixelShift.y;
  return { x, y };
};

const degreesToRadians = degrees => {
  degrees = 90 - degrees;
  return degrees * (Math.PI / 180);
};

const initMap = (maxX, maxY) => {
  calculateCanvasArea();
  app.value = new PIXI.Application({
    background: "#FFFFFF",
    width: canvasArea.width,
    height: canvasArea.height,
    resolution: window.devicePixelRatio || 1, // Adjust for device resolution
    autoDensity: true // Adjust canvas size for device resolution
  });

  if (mapContainer.value) {
    mapContainer.value.appendChild(app.value.view);
    mapElementsContainer.value = new PIXI.Container();
    app.value.stage.addChild(mapElementsContainer.value);
    calMapScaler(maxX, maxY);
  }
};

const addMapMarkers = mapMarkers => {
  mapMarkers.forEach(marker => {
    if (marker.objType == 0) {
      addACR(marker);
    } else if (marker.objType == 1) {
      addPoint(marker);
    } else if (marker.objType == 2) {
      addObstacle(marker);
    }
    inited.value++;
  });
};

const addACR = marker => {
  let x = parseFloat(marker.xcoordinate);
  let y = parseFloat(marker.ycoordinate);
  let angle = parseFloat(marker.angle);
  let width = parseFloat(marker.objWidth);
  let length = parseFloat(marker.objLength);
  let widthPx = parseFloat(marker.objWidthPx);
  let lengthPx = parseFloat(marker.objLengthPx);

  const pixcelCoord = convertMetersToPixels(x, y);

  const acrSprite = PIXI.Sprite.from(acrPng);
  acrSprite.anchor.set(0.5);
  acrSprite.x = pixcelCoord.x;
  acrSprite.y = pixcelCoord.y;
  acrSprite.scale.x = width / (widthPx / meterToPixelScale.x);
  acrSprite.scale.y = length / (lengthPx / meterToPixelScale.y);
  acrSprite.rotation = degreesToRadians(angle);

  // @ts-ignore
  acrSprite.name = marker.objName;
  mapElementsContainer.value.addChild(acrSprite);
};

const addPoint = marker => {
  let x = parseFloat(marker.xcoordinate);
  let y = parseFloat(marker.ycoordinate);

  const pixelCoord = convertMetersToPixels(x, y);
  const point = new PIXI.Graphics();
  point.beginFill(0x00ff00); // Green color for the point
  point.drawCircle(pixelCoord.x, pixelCoord.y, 3); // Small circle with radius of 5 pixels
  point.endFill();

  // @ts-ignore
  point.name = marker.objName;
  mapElementsContainer.value.addChild(point);
};

const addObstacle = marker => {
  let x = parseFloat(marker.xcoordinate);
  let y = parseFloat(marker.ycoordinate);
  let width = parseFloat(marker.objWidth);
  let length = parseFloat(marker.objLength);

  const pixelCoord = convertMetersToPixels(x, y);
  const obstacle = new PIXI.Graphics();
  obstacle.lineStyle(2, 0x000000); // Red color for the obstacle
  // Draw rectangle with conversion from meters to pixels
  obstacle.drawRect(
    pixelCoord.x - (width * meterToPixelScale.x) / 2,
    pixelCoord.y - (length * meterToPixelScale.y) / 2,
    width * meterToPixelScale.x,
    length * meterToPixelScale.y
  );
  obstacle.endFill();

  // @ts-ignore
  obstacle.name = marker.objName;
  mapElementsContainer.value.addChild(obstacle);
};

const updateMapMarkers = () => {
  let mapMarkers = useAcrStoreHook().getMapMarkers;
  if (inited.value < mapMarkers.length) return;
  mapMarkers.forEach(marker => {
    if (marker.objType == 0) {
      updateACR(marker);
    } else if (marker.objType == 1) {
      updatePoint(marker);
    } else if (marker.objType == 2) {
      updateObstacle(marker);
    }
  });
};

const updateACR = marker => {
  let x = useAcrStoreHook().getAcrInfo.xcoordinate;
  let y = useAcrStoreHook().getAcrInfo.ycoordinate;
  let angle = useAcrStoreHook().getAcrInfo.angle;
  let width = parseFloat(marker.objWidth);
  let length = parseFloat(marker.objLength);
  let widthPx = parseFloat(marker.objWidthPx);
  let lengthPx = parseFloat(marker.objLengthPx);

  const pixcelCoord = convertMetersToPixels(x, y);
  const acrSprite = mapElementsContainer.value.getChildByName(marker.objName);

  acrSprite.x = pixcelCoord.x;
  acrSprite.y = pixcelCoord.y;
  acrSprite.scale.x = width / (widthPx / meterToPixelScale.x);
  acrSprite.scale.y = length / (lengthPx / meterToPixelScale.y);
  acrSprite.rotation = degreesToRadians(angle);
};

const updatePoint = marker => {
  let x = parseFloat(marker.xcoordinate);
  let y = parseFloat(marker.ycoordinate);

  const pixelCoord = convertMetersToPixels(x, y);
  const point = mapElementsContainer.value.getChildByName(marker.objName);
  point.clear();
  point.beginFill(0x00ff00); // Green color for the point
  point.drawCircle(pixelCoord.x, pixelCoord.y, 3); // Small circle with radius of 5 pixels
  point.endFill();
};

const updateObstacle = marker => {
  let x = parseFloat(marker.xcoordinate);
  let y = parseFloat(marker.ycoordinate);
  let width = parseFloat(marker.objWidth);
  let length = parseFloat(marker.objLength);

  const pixelCoord = convertMetersToPixels(x, y);
  const obstacle = mapElementsContainer.value.getChildByName(marker.objName);
  obstacle.clear();
  obstacle.lineStyle(2, 0x000000); // Red color for the obstacle
  // Draw rectangle with conversion from meters to pixels
  obstacle.drawRect(
    pixelCoord.x - (width * meterToPixelScale.x) / 2,
    pixelCoord.y - (length * meterToPixelScale.y) / 2,
    width * meterToPixelScale.x,
    length * meterToPixelScale.y
  );
  obstacle.endFill();
};

defineExpose({
  updateMapMarkers
});
</script>

<template>
  <div>
    <div
      ref="mapContainer"
      style="border: 1px solid #32a1ce; min-width: 800px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
