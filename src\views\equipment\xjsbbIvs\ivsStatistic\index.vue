<script setup lang="ts">
import { onBeforeMount, reactive, ref } from "vue";
import { iVSPosAPI } from "/@/api/local/ivsPos";
import Local from "./local/index.vue";
import Server from "./server-web/index.vue";
import LocalLine from "../ivs/local/index.vue";
import ServerLine from "../ivs/server-web/index.vue";
import { XJSBB } from "/@/api/xjsbb/xjsbb";

const pageData = reactive({
  groupList: [],
  searchInfo: {
    groupId: "",
    groupName: ""
  }
});

// 是否需要展示
const isLocal = ref(0);

// 获取数据的函数，传递时间参数
onBeforeMount(() => {
  //isLocal.value = 2;
  iVSPosAPI
    .getIVSInfo()
    .then(res => {
      if (res === "empty") {
        isLocal.value = 2;
      } else {
        isLocal.value = 1;
      }
    })
    .catch(_err => {
      isLocal.value = 2;
    })
    .finally(() => {
      if (isLocal.value === 2) {
        XJSBB.getList().then((res: any) => {
          pageData.groupList = res;
          pageData.searchInfo.groupId = pageData.groupList[0].groupId;
          const groupNames = pageData.groupList[0].groupName.split("_");
          pageData.searchInfo.groupName = groupNames[groupNames.length - 1];
        });
      }
    });
});
</script>

<template>
  <div>
    <div v-if="isLocal === 2">
      <!-- 新增卡片 -->
      <el-card class="query-card">
        <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
          <el-form-item label="Line ID">
            <el-select
              v-model="pageData.searchInfo.groupId"
              placeholder="Line ID"
            >
              <el-option
                v-for="item in pageData.groupList"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
      <Server
        :groupList="pageData.groupList"
        :searchInfo="pageData.searchInfo"
      />
      <ServerLine
        v-auth="['icsYield:update']"
        :groupList="pageData.groupList"
        :searchInfo="pageData.searchInfo"
      />
    </div>
    <div v-if="isLocal === 1">
      <Local />
      <LocalLine />
    </div>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}
</style>
