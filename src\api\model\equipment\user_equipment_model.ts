import { BaseClass, BaseQuery } from "../domain";

export interface EquipmentQuery extends BaseQuery {
  equipmentName?: string;
  jarId?: string;
  equipmentType?: string;
}

export interface Equipment extends BaseClass {
  id?: string;
  equipmentId?: string;
  equipmentName?: string;
  equipmentCode?: string;
  equipmentType?: string;
  jarId?: string;
  address?: string;
  parentId?: string;
  fileUserDomain?: string;
  fileUserAccount?: string;
  fileUserPws?: string;
}

export interface EquipmentSaveParam extends BaseClass {
  id?: string;
  equipmentName?: string;
  jarId?: string;
  equipmentType?: string;
  equipmentCode?: string;
  address?: string;
  parentId?: string;
  fileUserDomain?: string;
  fileUserAccount?: string;
  fileUserPws?: string;
}

export interface EquipmentJarId {
  id?: string;
  jarId?: string;
}
