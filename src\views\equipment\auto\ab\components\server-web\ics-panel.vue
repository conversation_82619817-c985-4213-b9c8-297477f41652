<script setup lang="ts">
import { onBeforeMount, onMounted, ref } from "vue";
import icsChart from "./ics-chart.vue";
import { abAutoAdjustAPIs } from "/@/api/ab/abAutoAdjust";
import { emitter } from "/@/utils/mitt";
import { useICSAutoStoreHook } from "/@/store/modules/icsAuto";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object
  },
  pageWidth: {
    require: true,
    type: Number
  },
  pageHeight: {
    require: true,
    type: Number
  }
});

const leftTRightF = ref(true);

const onOffRule = ref({ enabled: "0" });

onBeforeMount(() => {
  if (props.icsEquipInfo == null) {
    return;
  }
  leftTRightF.value = props.icsEquipInfo.clientCode.includes("-L-");
});

onMounted(() => {
  getRule();
});

const getRule = () => {
  let groupId = useICSAutoStoreHook().getGroupId;
  abAutoAdjustAPIs
    .fetchRules({ groupId: groupId, equipId: props.icsEquipInfo.id, type: 4 })
    .then((res: any) => {
      if (res !== "fail" && res.length > 0) {
        onOffRule.value = res[0];
      }
    })
    .catch(_err => {
      console.log("error");
    });
};

const switchRule = () => {
  abAutoAdjustAPIs
    .switchRule(onOffRule.value)
    .then((_res: any) => {
      getRule();
    })
    .catch(_err => {
      console.log("error");
    });
};

const icsChartActiveNames = ref([]);

const chart1 = ref(null);
const chart2 = ref(null);

const handleIcsChartActiveChange = () => {
  if (
    icsChartActiveNames.value.indexOf("L1") >= 0 ||
    icsChartActiveNames.value.indexOf("R1") >= 0
  ) {
    chart1.value.subChartOpen = true;
  } else {
    setTimeout(() => {
      chart1.value.subChartOpen = false;
    }, 300);
  }
  if (
    icsChartActiveNames.value.indexOf("L2") >= 0 ||
    icsChartActiveNames.value.indexOf("R2") >= 0
  ) {
    chart2.value.subChartOpen = true;
  } else {
    setTimeout(() => {
      chart2.value.subChartOpen = false;
    }, 300);
  }
};

emitter.on("reloadAutoIcsCharts", () => {
  setTimeout(() => {
    chart1.value.callReload();
    chart2.value.callReload();
  }, 1000);
});
</script>

<template>
  <el-row align="middle">
    <span style="font-weight: bold; font-size: 16px">
      {{ leftTRightF ? "Auto Adjust AB-L" : "Auto Adjust AB-R" }}
    </span>
    <el-switch
      v-model="onOffRule.enabled"
      @change="switchRule"
      inline-prompt
      style="
        --el-switch-on-color: #13ce66;
        --el-switch-off-color: #ff4949;
        margin-left: 20px;
      "
      active-value="1"
      inactive-value="0"
      active-text="On"
      inactive-text="Off"
      :disabled="true"
    />
  </el-row>
  <el-collapse
    v-model="icsChartActiveNames"
    @change="handleIcsChartActiveChange"
  >
    <icsChart
      :equip-id="props.icsEquipInfo.id"
      :mark="leftTRightF ? 'L' : 'R'"
      :mark-num="'A1'"
      :page-width="props.pageWidth"
      :page-height="props.pageHeight"
      ref="chart1"
    />
    <icsChart
      :equip-id="props.icsEquipInfo.id"
      :mark="leftTRightF ? 'L' : 'R'"
      :mark-num="'B1'"
      :page-width="props.pageWidth"
      :page-height="props.pageHeight"
      ref="chart2"
    />
  </el-collapse>
</template>

<style scoped></style>
