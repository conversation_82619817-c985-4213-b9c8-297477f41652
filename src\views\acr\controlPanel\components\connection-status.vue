<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useAcrStoreHook } from "/@/store/modules/acr";

defineOptions({
  name: "ConnectionStatus"
});

const equipConnectStatus = ref([]);

onMounted(() => {
  let intervalId = setInterval(() => {
    equipConnectStatus.value = useAcrStoreHook().getDispatcherStatus;
    if (equipConnectStatus.value.length > 0) {
      clearInterval(intervalId);
    }
  }, 100); // Check every 100ms
  console.log("ConnectionStatus mounted");
});
</script>

<template>
  <el-descriptions size="small" :column="1" direction="vertical">
    <el-descriptions-item
      label="Equipment Online Status"
      label-class-name="my-label"
      align="center"
    >
      <el-descriptions
        size="small"
        :column="equipConnectStatus.length"
        direction="vertical"
      >
        <el-descriptions-item
          v-for="equip in equipConnectStatus"
          :key="equip.equipName"
          :label="equip.equipName"
          align="center"
          :width="100 / equipConnectStatus.length + '%'"
        >
          <el-badge
            :value="equip.connectStatus == 1 ? 'Online' : 'OffLine'"
            :type="equip.connectStatus == 1 ? 'success' : 'danger'"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style lang="scss" scoped>
:deep(.my-label) {
  font-weight: bold !important;
  font-size: 1rem !important;
}

.el-badge {
  margin-left: 0;
  margin-top: 0.2rem;
  padding: 0;
}
</style>
