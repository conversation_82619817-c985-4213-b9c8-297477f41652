<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, reactive } from "vue";
import { abAutoAdjustAPIs } from "/@/api/ab/abAutoAdjust";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const manuelData = reactive({
  ra: {
    value: "",
    focus: false
  },
  rb: {
    value: "",
    focus: false
  },
  gtFormula1: {
    value: "",
    focus: false,
    head: "UP"
  },
  gtFormula2: {
    value: "",
    focus: false,
    head: "UP"
  },
  gtFormula3: {
    value: "",
    focus: false,
    head: "DN"
  },
  gtFormula4: {
    value: "",
    focus: false,
    head: "DN"
  }
});

const selectedValue = ref("");

let timer = null;

onMounted(() => {
  selectedValue.value = "ics_l";
  timer = setInterval(() => {
    fetchPlcData();
  }, 2000);
});

const clearTimer = () => {
  clearInterval(timer);
  timer = null;
};

onBeforeUnmount(() => {
  clearTimer();
});

const fetchPlcData = () => {
  abAutoAdjustAPIs
    .fetchManuelData({
      equipId: props.icsEquipInfo[selectedValue.value].id
    })
    .then(res => {
      if (res !== "fail") {
        if (!manuelData.ra.focus) {
          manuelData.ra.value = res.ra === undefined ? "" : res.ra;
        }
        if (!manuelData.rb.focus) {
          manuelData.rb.value = res.rb === undefined ? "" : res.rb;
        }
        if (!manuelData.gtFormula1.focus) {
          manuelData.gtFormula1.value = res.gtFormula1;
        }
        if (!manuelData.gtFormula2.focus) {
          manuelData.gtFormula2.value = res.gtFormula2;
        }
        if (!manuelData.gtFormula3.focus) {
          manuelData.gtFormula3.value = res.gtFormula3;
        }
        if (!manuelData.gtFormula4.focus) {
          manuelData.gtFormula4.value = res.gtFormula4;
        }
      }
    })
    .catch(err => {
      console.log(err);
    });
};

const writeData = parameter => {
  setFocus(parameter);
  ElMessageBox({
    title: "Please Confirm Write!",
    message:
      "Write " +
      parameter.toUpperCase() +
      " value:" +
      manuelData[parameter].value,
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      abAutoAdjustAPIs
        .writePlcData({
          equipId: props.icsEquipInfo[selectedValue.value].id,
          parameter: parameter,
          value: manuelData[parameter].value
        })
        .then((res: any) => ElMessageBox.alert(res))
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          manuelData[parameter].focus = false;
        });
    })
    .catch(() => (manuelData[parameter].focus = false));
};

const saveFormula = parameter => {
  setFocus(parameter);
  ElMessageBox({
    title: "Please Confirm Update!",
    message: "Update Formula to: Y=" + manuelData[parameter].value,
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      abAutoAdjustAPIs
        .updateFormula({
          equipId: props.icsEquipInfo[selectedValue.value].id,
          parameter: parameter,
          formula: manuelData[parameter].value
        })
        .then((res: any) => ElMessageBox.alert(res))
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          manuelData[parameter].focus = false;
        });
    })
    .catch(() => (manuelData[parameter].focus = false));
};

const setFocus = parameter => {
  manuelData[parameter].focus = true;
};
const resetFocus = parameter => {
  manuelData[parameter].focus = false;
};
</script>

<template>
  <el-descriptions border size="large" :column="1" style="margin-top: 5px">
    <el-descriptions-item label="Manual" label-align="center" align="left">
      <el-row align="middle">
        Select AB L/R:
        <el-select
          v-model="selectedValue"
          placeholder="Select L/R"
          :clearable="false"
        >
          <el-option label="AB-L" value="ics_l" />
          <el-option label="AB-R" value="ics_r" />
        </el-select>
      </el-row>
      <el-row>
        <el-col :span="8">
          A:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.ra.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('ra')"
                @blur="resetFocus('ra')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('ra')"
                :disabled="true"
                v-auth="['autoab:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
        <el-col :span="8">
          B:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.rb.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('rb')"
                @blur="resetFocus('rb')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('rb')"
                v-auth="['autoab:update']"
                :disabled="true"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          A
          <span style="color: red">UP</span> ! Adjust Formula(x=A's Target - ▲A,
          ▲A equals to rule triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula1')"
                @blur="resetFocus('gtFormula1')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula1')"
                v-auth="['autoab:update']"
                :disabled="true"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          A <span style="color: red">DOWN</span> Adjust Formula(x=A's Target -
          ▲A, ▲A equals to rule triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula3.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula3')"
                @blur="resetFocus('gtFormula3')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula3')"
                v-auth="['autoab:update']"
                :disabled="true"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          B
          <span style="color: red">UP</span> ! Adjust Formula(x=B's Target - ▲B,
          ▲B equals to rule triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula2')"
                @blur="resetFocus('gtFormula2')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula2')"
                v-auth="['autoab:update']"
                :disabled="true"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          B <span style="color: red">DOWN</span> Adjust Formula(x=B's Target -
          ▲B, ▲B equals to rule triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula4.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula4')"
                @blur="resetFocus('gtFormula4')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula4')"
                v-auth="['autoab:update']"
                :disabled="true"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped></style>
