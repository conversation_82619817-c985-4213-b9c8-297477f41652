<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { abAutoAdjustAPI } from "/@/api/local/abAutoAdjust";

const flagStatus = ref({ flag: false });

const fetchFlagStatus = async () => {
  try {
    const res = await abAutoAdjustAPI.getabKv8000WriteFlag();
    if (res !== "fail") {
      flagStatus.value.flag = res;
    } else {
      throw new Error("获取标志状态失败");
    }
  } catch (error) {
    console.error("获取标志状态时出错:", error);
    ElMessage.error("获取标志状态失败");
  }
};

const toggleFlag = async () => {
  try {
    const res = await abAutoAdjustAPI.writeabKv8000WriteFlag();
    if (res !== "fail") {
      !flagStatus.value.flag;
      ElMessage.info(`标志状态更新为: ${flagStatus.value.flag.toString()}`);
    } else {
      throw new Error("更新标志失败");
    }
  } catch (error) {
    console.error("更新标志状态时出错:", error);
    ElMessage.error("更新标志失败");
  }
};

onMounted(() => {
  fetchFlagStatus();
});
</script>

<template>
  <el-descriptions border size="large" :column="1" style="margin-top: 5px">
    <el-descriptions-item label="KV8000 写入" label-align="center" align="left">
      <el-row align="middle">
        <el-col :span="12">状态：</el-col>
        <el-col :span="12">
          <el-switch
            v-model="flagStatus.flag"
            active-text="已启用"
            inactive-text="已禁用"
            @change="toggleFlag"
            v-auth="['autoab:update']"
          />
        </el-col>
      </el-row>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}
</style>
