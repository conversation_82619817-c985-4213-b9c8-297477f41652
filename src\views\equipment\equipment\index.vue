<script setup lang="ts">
import { equipmentAPI } from "/@/api/equipment/equipment";
import { equipGroupAPI } from "/@/api/equipment/equipGroup";
import { onMounted, reactive } from "vue";
import {
  Equipment,
  EquipmentQuery,
  EquipmentJarId,
  EquipGroupVo
} from "/@/api/model/equipment/equipment_model";
import { EntityType, Page } from "/@/api/model/domain";
import addIndex from "./addIndex.vue";
import equipParameter from "./equipParameter.vue";
import equipGroup from "./equipGroup.vue";
import updateIndex from "./updateIndex.vue";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";
import setting from "/@/assets/svg/setting.svg?component";

defineOptions({
  name: "EquipmentManage"
});

const pageData = reactive<{
  loading: boolean;
  selection: Equipment[];
  testList: Equipment[];
  searchTestInfo: EquipmentQuery;
  addIndexPage: boolean;
  updateIndexPage: boolean;
  parameterPage: boolean;
  equipGroupPage: boolean;
  JarDetailList: EquipmentJarId[];
  updateEntity: Equipment;
  equipmentTypeList: EntityType[];
  equipGroupList: EquipGroupVo[];
}>({
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    equipmentName: "",
    equipmentType: "",
    jarId: "",
    groupId: null,
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  },
  addIndexPage: false,
  updateIndexPage: false,
  parameterPage: false,
  equipGroupPage: false,
  JarDetailList: [],
  updateEntity: null,
  equipmentTypeList: [],
  equipGroupList: []
});

const getPage = () => {
  pageData.loading = true;
  equipmentAPI
    .page(pageData.searchTestInfo)
    .then((res: Page<Equipment[]> | string) => {
      if (res !== "fail") {
        pageData.testList = (res as Page<Equipment[]>).records;
        pageData.searchTestInfo.total = Number(
          (res as Page<Equipment[]>).total
        );
      }
    })
    .finally(() => (pageData.loading = false));
};

const getJarIdList = () => {
  equipmentAPI.getJarIdList().then((res: EquipmentJarId[] | string) => {
    if (res !== "fail") {
      pageData.JarDetailList = res as EquipmentJarId[];
    }
  });
};

const getTypeList = () => {
  equipmentAPI.getEquipmentTypeList().then((res: EntityType[] | string) => {
    if (res !== "fail") {
      pageData.equipmentTypeList = res as EntityType[];
    }
  });
};

const syncOcrEquip = () => {
  equipmentAPI.syncOcrIcsEquip().then(() => ElMessageBox.alert("SUCCESS"));
};

const getGroupList = () => {
  equipGroupAPI.getList().then((res: EquipGroupVo[] | string) => {
    if (res !== "fail") {
      pageData.equipGroupList = res as EquipGroupVo[];
    }
  });
};

onMounted(() => {
  getPage();
  getGroupList();
  getJarIdList();
  getTypeList();
});

const handlerSearch = () => {
  pageData.searchTestInfo.pageNum = 1;
  getPage();
};

const handelSelectionChange = val => {
  pageData.selection = val;
};

const sizeChange = (pageSize: number) => {
  pageData.searchTestInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchTestInfo.pageNum = pageNum;
  getPage();
};

const openAddIndexPage = () => {
  pageData.addIndexPage = true;
};

const closeAddIndexPage = () => {
  pageData.addIndexPage = false;
  getPage();
};

const openUpdateIndexPage = (vo: Equipment) => {
  pageData.updateEntity = vo;
  pageData.updateIndexPage = true;
};

const closeUpdateIndexPage = () => {
  pageData.updateEntity = null;
  pageData.updateIndexPage = false;
  getPage();
};

const openParameterPage = (vo: Equipment) => {
  pageData.updateEntity = vo;
  pageData.parameterPage = true;
};

const closeParameterPage = () => {
  pageData.updateEntity = null;
  pageData.parameterPage = false;
};

const openGroupPage = () => {
  pageData.equipGroupPage = true;
};

const closeGroupPage = () => {
  pageData.equipGroupPage = false;
  getGroupList();
  pageData.searchTestInfo.groupId = null;
};

const deleteBatch = () => {
  if (pageData.selection.length > 0) {
    let ids: string[] = [];
    pageData.selection.forEach(item => ids.push(item.id));
    ElMessageBox.confirm("Are you delete batch?")
      .then(() => {
        equipmentAPI.deleteBatch(ids).then(() => {
          getPage();
          getJarIdList();
        });
      })
      .catch(() => {
        // catch error
      });
  }
};

const deleteById = (id: string) => {
  ElMessageBox.confirm("Are you delete batch?")
    .then(() => {
      equipmentAPI.deleteById(id).then(() => {
        getPage();
        getJarIdList();
      });
    })
    .catch(() => {
      // catch error
    });
};

const getEquipmentType = data => {
  const type = data.equipmentType;
  const etn = pageData.equipmentTypeList;
  for (const item in etn) {
    if (etn[item].value == type) {
      return transformI18n(etn[item].name);
    }
  }
  return null;
};
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-input
          v-model="pageData.searchTestInfo.equipmentName"
          :placeholder="transformI18n('equipment.equipmentName')"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.groupId"
          :placeholder="transformI18n('equipment.groupName')"
          clearable
        >
          <el-option
            v-for="item in pageData.equipGroupList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
        <el-button
          type="primary"
          plain
          style="width: 30px; height: 30px"
          @click="openGroupPage"
        >
          <el-icon style="vertical-align: middle">
            <setting />
          </el-icon>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.equipmentType"
          :placeholder="transformI18n('equipment.equipmentType')"
          clearable
        >
          <el-option
            v-for="item in pageData.equipmentTypeList"
            :key="item.value"
            :label="transformI18n(item.name)"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.jarId"
          :placeholder="transformI18n('equipment.jarId')"
          clearable
        >
          <el-option
            v-for="item in pageData.JarDetailList"
            :key="item.id"
            :label="item.jarId"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="handlerSearch">{{
          transformI18n("buttons.hssearch")
        }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="openAddIndexPage">{{
          transformI18n("buttons.hsadd")
        }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="deleteBatch">{{
          transformI18n("buttons.hsdelete")
        }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          plain
          @click="syncOcrEquip"
          title="将OCR数据库中ICS设备同步到列表中来"
          >{{ transformI18n("buttons.hssync") }}</el-button
        >
      </el-form-item>
      <add-index
        v-if="pageData.addIndexPage"
        :closeCallBack="closeAddIndexPage"
        :EquipmentType="pageData.equipmentTypeList"
        :equipGroupList="pageData.equipGroupList"
      />
      <update-index
        v-if="pageData.updateIndexPage"
        :closeCallBack="closeUpdateIndexPage"
        :entity="pageData.updateEntity"
        :EquipmentType="pageData.equipmentTypeList"
        :equipGroupList="pageData.equipGroupList"
      />
      <equip-parameter
        v-if="pageData.parameterPage"
        :closeCallBack="closeParameterPage"
        :entity="pageData.updateEntity"
      />
      <equip-group
        v-if="pageData.equipGroupPage"
        :equipGroupList="pageData.equipGroupList"
        :closeCallBack="closeGroupPage"
      />
    </el-form>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        @selection-change="handelSelectionChange"
      >
        <el-table-column
          resizable
          :show-overflow-tooltip="true"
          type="selection"
        />
        <el-table-column
          prop="equipmentName"
          :label="transformI18n('equipment.equipmentName')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="equipmentType"
          :label="transformI18n('equipment.equipmentType')"
          resizable
          :show-overflow-tooltip="true"
          :formatter="getEquipmentType"
        />
        <el-table-column
          prop="jarId"
          :label="transformI18n('equipment.jarId')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="address"
          :label="transformI18n('equipment.address')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="groupName"
          :label="transformI18n('equipment.groupName')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserDomain"
          :label="transformI18n('equipment.fileUserDomain')"
          sortable
          v-if="false"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserAccount"
          :label="transformI18n('equipment.fileUserAccount')"
          sortable
          v-if="false"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserPws"
          v-if="false"
          :label="transformI18n('equipment.fileUserPws')"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column
          fixed="right"
          :label="transformI18n('buttons.hsoperations')"
          width="130px"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="openUpdateIndexPage(scope.row)"
              >{{ transformI18n("buttons.hsupdate") }}</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="deleteById(scope.row.id)"
              >{{ transformI18n("buttons.hsdelete") }}</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              v-if="scope.row.equipmentType == 7"
              @click="openParameterPage(scope.row)"
              >{{ transformI18n("equipment.settingParameter") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchTestInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchTestInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchTestInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped></style>
