<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { transformI18n } from "/@/plugins/i18n";
import { roleAPI } from "/@/api/system/role";
import { ElMessageBox, FormRules } from "element-plus";

const drawer = ref(true);

const props = defineProps({
  entity: {
    type: Object,
    default: null
  },
  closeCallBack: {
    type: Function
  },
  lastSortNum: {
    type: Number
  }
});

const loading = ref(false);

const addTree = ref();

const formData = reactive({
  id: 0,
  roleName: "",
  role: "",
  type: null,
  sort: 0,
  createdAt: "",
  updatedAt: "",
  permissions: [],
  checkedPermIds: []
});

const beforeEditedForm = reactive({
  role: "",
  roleName: "",
  checkedPermIds: []
});

onMounted(() => {
  buildFormData();
});

const buildFormData = () => {
  if (props.entity === null) {
    // init new role here
    formData.id = 100;
    formData.roleName = "";
    formData.role = "";
    formData.type = 1;
    formData.sort = props.lastSortNum;
    formData.createdAt = "";
    formData.updatedAt = "";
  } else {
    formData.id = props.entity.id;
    formData.roleName = props.entity.roleName;
    formData.role = props.entity.role;
    formData.type = props.entity.type;
    formData.sort = props.entity.sort;
    formData.createdAt = props.entity.createdAt;
    formData.updatedAt = props.entity.updatedAt;
  }
  beforeEditedForm.role = formData.role;
  beforeEditedForm.roleName = formData.roleName;

  loading.value = true;
  roleAPI
    .getPermissionOptions(formData.id)
    .then((res: any) => {
      if (res !== "fail" && res !== null) {
        formData.permissions = [];
        formData.permissions = res.permissions;
        formData.checkedPermIds = res.checkedIds;
        beforeEditedForm.checkedPermIds = res.checkedIds;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const onSave = () => {
  if (formData.role === "" || formData.roleName === "") {
    ElMessageBox.alert(transformI18n("role.edit-save-check") + ".");
  } else {
    roleAPI
      .save(formData)
      .then(res => {
        beforeEditedForm.role = formData.role;
        beforeEditedForm.roleName = formData.roleName;
        beforeEditedForm.checkedPermIds = formData.checkedPermIds;
        ElMessageBox.alert(res || "Save Success!");
      })
      .catch(e => {
        console.log(e);
      });
  }
};

const onCancel = () => {
  if (!isIdentical()) {
    ElMessageBox({
      title: transformI18n("dialogue.title-warning"),
      message: transformI18n("dialogue.msg-confirm-not-saved"),
      confirmButtonText: transformI18n("dialogue.confirm"),
      cancelButtonText: transformI18n("dialogue.cancel"),
      showCancelButton: true
    })
      .then(() => {
        drawer.value = false;
      })
      .catch(_err => {});
  } else {
    drawer.value = false;
  }
};

const onCloseCheck = done => {
  if (!isIdentical()) {
    ElMessageBox({
      title: transformI18n("dialogue.title-warning"),
      message: transformI18n("dialogue.msg-confirm-not-saved"),
      confirmButtonText: transformI18n("dialogue.confirm"),
      cancelButtonText: transformI18n("dialogue.cancel"),
      showCancelButton: true
    })
      .then(() => {
        done();
      })
      .catch(_err => {});
  } else {
    done();
  }
};

const isIdentical = () => {
  let isIdentical = true;
  if (beforeEditedForm.role !== formData.role) {
    isIdentical = false;
  }
  if (beforeEditedForm.roleName !== formData.roleName) {
    isIdentical = false;
  }

  let longerArr,
    shorterArr = [];
  if (beforeEditedForm.checkedPermIds.length > formData.checkedPermIds.length) {
    longerArr = beforeEditedForm.checkedPermIds;
    shorterArr = formData.checkedPermIds;
  } else {
    longerArr = formData.checkedPermIds;
    shorterArr = beforeEditedForm.checkedPermIds;
  }
  // 取差集，即long - short > 0则证明修改过选项
  if (longerArr.filter(c => !shorterArr.includes(c)).length > 0) {
    isIdentical = false;
  }
  return isIdentical;
};

const addPermissionIdListChange = () => {
  formData.checkedPermIds = addTree.value.getCheckedKeys().filter(p => p >= 0);
};

const treeProps = {
  children: "child",
  label: (data, _node) => {
    return transformI18n(data.label);
  },
  disabled: (_data, _node): string => {
    // 无法修改管理员权限
    // @ts-ignore
    return formData.id == 0 ? true : false;
    // return false;
  }
};

const rules = reactive<FormRules>({
  role: [
    {
      required: true,
      message: "Please input Role",
      trigger: "blur"
    }
  ],
  roleName: [
    {
      required: true,
      message: "Please input Role Name",
      trigger: "blur"
    }
  ]
});
</script>

<template>
  <div>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      @close="props.closeCallBack"
      :before-close="onCloseCheck"
      :close-on-click-modal="false"
      size="35%"
    >
      <template #header>
        <span style="font-weight: bold">
          {{
            formData.id == 100
              ? transformI18n("role.edit-form-add-title")
              : transformI18n("role.edit-form-update-title")
          }}
        </span>
      </template>
      <template #default>
        <div>
          <el-form
            :inline="true"
            :model="formData"
            label-width="150px"
            status-icon
            :rules="rules"
          >
            <el-form-item
              :label="transformI18n('role.edit-form-roleName') + ':'"
              prop="roleName"
              style="width: 80%"
            >
              <el-input
                v-model="formData.roleName"
                :disabled="formData.id < 100 ? true : false"
              />
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-role') + ':'"
              prop="role"
              style="width: 80%"
            >
              <el-input
                v-model="formData.role"
                :disabled="formData.id < 100 ? true : false"
              />
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-type') + ':'"
              prop="type"
              style="width: 80%; font-weight: 700"
            >
              <el-tag :type="formData.type === 0 ? 'danger' : ''">{{
                formData.type === 0
                  ? transformI18n("role.query-select-system")
                  : transformI18n("role.query-select-custom")
              }}</el-tag>
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-sort') + ':'"
              prop="sort"
              style="width: 80%"
            >
              <el-input v-model="formData.sort" :disabled="true" />
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-createdAt') + ':'"
              prop="createdAt"
              style="width: 80%"
            >
              <el-input v-model="formData.createdAt" :disabled="true" />
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-updatedAt') + ':'"
              prop="updatedAt"
              style="width: 80%"
            >
              <el-input v-model="formData.updatedAt" :disabled="true" />
            </el-form-item>
            <el-form-item
              :label="transformI18n('role.edit-form-permissions') + ':'"
              prop="permissions"
              style="width: 80%; font-weight: 700"
              :loading="loading"
            >
              <el-tree
                :data="formData.permissions"
                :default-checked-keys="formData.checkedPermIds"
                show-checkbox
                node-key="permId"
                :props="treeProps"
                ref="addTree"
                @check-change="addPermissionIdListChange"
                empty-text=""
              />
            </el-form-item>
          </el-form>
        </div>
      </template>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="onSave">{{
            transformI18n("buttons.hssave")
          }}</el-button>
          <el-button @click="onCancel">{{
            transformI18n("buttons.hsclose")
          }}</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style scoped></style>
