<script setup lang="ts">
import { onBeforeMount, ref } from "vue";
import { icsTrendAPI } from "/@/api/local/icsTrend";
import Chart from "./chart.vue"; // 导入 chart 组件
import dayjs from "dayjs";

// 创建响应式变量
const chartDataLTemp = ref<any[]>([]);
const chartDataRTemp = ref<any[]>([]);
const chartDataLFlow = ref<any[]>([]);
const chartDataRFlow = ref<any[]>([]);
const chartDataLTotal = ref<any[]>([]);
const chartDataRTotal = ref<any[]>([]);
const temperatureHotAirL = ref<any[]>([]);
const temperatureLaserL = ref<any[]>([]);
const temperatureHotAirR = ref<any[]>([]);
const temperatureLaserR = ref<any[]>([]);
const xAxisDataL = ref<string[]>([]);
const xAxisDataR = ref<string[]>([]);
const N2xAxisDataL = ref<string[]>([]);
const N2xAxisDataR = ref<string[]>([]);
const hotAirAxisDataL = ref<string[]>([]);
const laserAxisDataL = ref<string[]>([]);
const hotAirAxisDataR = ref<string[]>([]);
const laserAxisDataR = ref<string[]>([]);

// 添加加载状态变量
const isLoading = ref(false);
const isLoadingHotLaser = ref(false);
// 悬浮导航相关
const isNavOpen = ref(false);
const activeChart = ref("");

// 定义图表导航项
const chartNavItems = [
  { id: "chart-ics-temp", title: "L-Temperature-Control" },
  { id: "chart-ics-flow", title: "L-Flow" },
  { id: "chart-ics-total", title: "L-N2Purity" },
  { id: "chart-r-ics-temp", title: "R-Temperature-Control" },
  { id: "chart-r-ics-flow", title: "R-Flow" },
  { id: "chart-r-ics-total", title: "R-N2Purity" },
  { id: "chart-l-hotair", title: "L-Temperature-HotAir" },
  { id: "chart-l-laser", title: "L-Temperature-Laser" },
  { id: "chart-r-hotair", title: "R-Temperature-HotAir" },
  { id: "chart-r-laser", title: "R-Temperature-Laser" }
];

// 滚动到指定图表
const scrollToChart = (chartId: string) => {
  const element = document.getElementById(chartId);
  if (element) {
    element.scrollIntoView({ behavior: "smooth", block: "center" });
    isNavOpen.value = false;
    activeChart.value = chartId;
  }
};

// 用于存储选中的日期范围和barCode
const selectedDateRange = ref<[string, string] | null>(null);
const selectedBarCode = ref<string>(""); // 新增的barCode筛选

// 获取数据的函数，传递时间范围和barCode参数
const fetchData = async (
  dateRange: [string, string] | null,
  barcode: string
) => {
  isLoading.value = true;
  isLoadingHotLaser.value = true;
  const params = dateRange
    ? { startDate: dateRange[0], endDate: dateRange[1], barcode }
    : { barcode };
  icsTrendAPI
    .getPosData(params)
    .then((res: any) => {
      if (res !== "fail") {
        processChartData(res);
      }
    })
    .finally(() => {
      isLoading.value = false;
    });
  icsTrendAPI
    .getHotLaserData(params)
    .then((res: any) => {
      if (res !== "fail") {
        processHotLaserData(res);
      }
    })
    .finally(() => {
      isLoadingHotLaser.value = false;
    });
};

// 数据处理函数，将不同的数据分配给对应的表格
const processChartData = (res: any) => {
  // 更新 chartData
  xAxisDataL.value = res["ICS-L-T"].xAxis;
  xAxisDataR.value = res["ICS-R-T"].xAxis;
  N2xAxisDataL.value = res["ICS-L-N2Purity"].xAxis;
  N2xAxisDataR.value = res["ICS-R-N2Purity"].xAxis;
  chartDataLTemp.value = getSeriesFromResData(res["ICS-L-T"]);
  chartDataRTemp.value = getSeriesFromResData(res["ICS-R-T"]);
  chartDataLFlow.value = getSeriesFromResData(res["ICS-L-F"]);
  chartDataRFlow.value = getSeriesFromResData(res["ICS-R-F"]);
  chartDataLTotal.value = getSeriesFromResData(res["ICS-L-N2Purity"]);
  chartDataRTotal.value = getSeriesFromResData(res["ICS-R-N2Purity"]);
};

// 数据处理函数，将不同的数据分配给对应的表格
const processHotLaserData = (res: any) => {
  // 更新 chartData
  hotAirAxisDataL.value = res["ICS-HotAir-L"].xAxis;
  hotAirAxisDataR.value = res["ICS-HotAir-R"].xAxis;
  laserAxisDataL.value = res["ICS-Laser-L"].xAxis;
  laserAxisDataR.value = res["ICS-Laser-R"].xAxis;
  temperatureHotAirL.value = getSeriesFromResData(res["ICS-HotAir-L"]);
  temperatureHotAirR.value = getSeriesFromResData(res["ICS-HotAir-R"]);
  temperatureLaserL.value = getSeriesFromResData(res["ICS-Laser-L"]);
  temperatureLaserR.value = getSeriesFromResData(res["ICS-Laser-R"]);
};
// 辅助函数，将数据转换为图表需要的格式
const getSeriesFromResData = (series: any) => {
  const seriesData: any[] = [];
  Object.keys(series).forEach(key => {
    if (key === "xAxis") {
      return;
    }
    seriesData.push({
      name: key,
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: series[key] || [] // 数据为 p0-p9 的值
    });
  });
  return seriesData;
};

// 日期范围选择限制，禁用今天以后的日期
const disabledDate = (date: Date) => {
  return date > new Date();
};

// 监听日期范围变化事件
const onDateRangeChange = (dateRange: [string, string]) => {
  selectedDateRange.value = dateRange;
  if (selectedDateRange.value) {
    fetchData(dateRange, selectedBarCode.value); // 当选择日期范围时，重新获取数据
  }
};

// 监听barCode选择变化事件
const onBarCodeChange = (barCode: string) => {
  selectedBarCode.value = barCode;
  fetchData(selectedDateRange.value, barCode); // 当选择barCode时，重新获取数据
};

onBeforeMount(() => {
  const today = dayjs().format("YYYY-MM-DD HH:mm:ss");
  const yesterday = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
  selectedDateRange.value = [yesterday, today];
  fetchData([yesterday, today], selectedBarCode.value); // 页面加载时默认获取过去一天的数据
});
</script>

<template>
  <div class="ics-trend-container">
    <!-- 悬浮导航按钮 -->
    <div class="floating-nav-button" @mouseenter="isNavOpen = true">
      <IconifyIconOffline
        class="icon-location"
        icon="location"
        @mouseenter="isNavOpen = true"
      />
    </div>

    <!-- 悬浮导航面板 -->
    <div class="floating-nav-panel" :class="{ 'is-open': isNavOpen }">
      <div class="nav-items">
        <div
          @mouseenter="isNavOpen = true"
          @mouseleave="isNavOpen = false"
          v-for="item in chartNavItems"
          :key="item.id"
          class="nav-item"
          @click="scrollToChart(item.id)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>

    <div>
      <!-- 时间范围和barCode筛选器 -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="12">
          <el-form :inline="true" label-width="60px" class="filter-form">
            <el-form-item label="Date:">
              <el-date-picker
                v-model="selectedDateRange"
                type="datetimerange"
                range-separator="To"
                start-placeholder="Start date"
                end-placeholder="End date"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                @change="onDateRangeChange"
                class="date-picker"
              />
            </el-form-item>
          </el-form>
        </el-col>

        <!-- BarCode 输入框 -->
        <el-col :span="12">
          <el-form :inline="true" label-width="120px" class="filter-form">
            <el-form-item label="barCode:">
              <el-input
                v-model="selectedBarCode"
                placeholder="Input select text"
                @input="onBarCodeChange(selectedBarCode)"
                class="barCode-input"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 渲染表格，每个表格占据整行 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-ics-temp"
            chartId="chart-ics-temp"
            :xLine="xAxisDataL"
            :yLine="chartDataLTemp"
            title="L-Temperature-Control"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-ics-flow"
            chartId="chart-ics-flow"
            :xLine="xAxisDataL"
            :yLine="chartDataLFlow"
            title="L-Flow"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-ics-total"
            chartId="chart-ics-total"
            :xLine="N2xAxisDataL"
            :yLine="chartDataLTotal"
            title="L-N2Purity"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-r-ics-temp"
            chartId="chart-r-ics-temp"
            :xLine="xAxisDataR"
            :yLine="chartDataRTemp"
            title="R-Temperature-Control"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-r-ics-flow"
            chartId="chart-r-ics-flow"
            :xLine="xAxisDataR"
            :yLine="chartDataRFlow"
            title="R-Flow"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-r-ics-total"
            chartId="chart-r-ics-total"
            :xLine="N2xAxisDataR"
            :yLine="chartDataRTotal"
            title="R-N2Purity"
            :loading="isLoading"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-l-hotair"
            chartId="chart-l-hotair"
            :xLine="hotAirAxisDataL"
            :yLine="temperatureHotAirL"
            title="L-Temperature-HotAir"
            :loading="isLoadingHotLaser"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-l-laser"
            chartId="chart-l-laser"
            :xLine="laserAxisDataL"
            :yLine="temperatureLaserL"
            title="L-Temperature-Laser"
            :loading="isLoadingHotLaser"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-r-hotair"
            chartId="chart-r-hotair"
            :xLine="hotAirAxisDataR"
            :yLine="temperatureHotAirR"
            title="R-Temperature-HotAir"
            :loading="isLoadingHotLaser"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            id="chart-r-laser"
            chartId="chart-r-laser"
            :xLine="laserAxisDataR"
            :yLine="temperatureLaserR"
            title="R-Temperature-Laser"
            :loading="isLoadingHotLaser"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.icon-location {
  width: 30px;
  height: 40px;
}

.ics-trend-container {
  position: relative;
}

.floating-nav-button {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background-color: rgba(138, 191, 245, 0.733); /* 半透明 */
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease;
}

.floating-nav-button:hover {
  background-color: rgb(55, 143, 231); /* 鼠标悬停时完全不透明 */
}

.floating-nav-panel {
  position: fixed;
  top: 50%;
  right: -250px;
  transform: translateY(-50%);
  width: 250px;
  background-color: rgba(95, 93, 93, 0.116); /* 半透明 */
  border-radius: 10px 0 0 10px;
  box-shadow: -2px 2px 12px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease, background-color 0.3s ease;
  z-index: 999;
}

.floating-nav-panel:hover {
  background-color: rgba(255, 255, 255, 1); /* 鼠标悬停时完全不透明 */
}

.floating-nav-panel.is-open {
  right: 0;
}

.nav-items {
  padding: 20px 0;
}

.nav-item {
  padding: 10px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease, opacity 0.3s ease;
  opacity: 0.7; /* 初始半透明 */
}

.nav-item:hover {
  background-color: #f0f0f0;
  opacity: 1; /* 鼠标悬停时完全不透明 */
}

.nav-item.active {
  background-color: #409eff;
  color: white;
}

.chart-row {
  margin-bottom: 80px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.date-picker {
  width: 200px;
}

.barCode-input {
  width: 200px;
}
</style>
