<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";

// 定义 props 类型
const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array,
    required: true
  },
  yLine: {
    type: Array,
    required: true
  },
  yAxisMin: {
    type: Number,
    default: 0
  },
  yAxisMax: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    required: true
  }
});
let chart: echarts.ECharts | null = null; // 存储ECharts实例;
// 计算样本数据的均值
function calculateMean(data) {
  const sum = data.reduce((acc, val) => acc + val, 0);
  return sum / data.length;
}

// 计算样本数据的标准差
function calculateStdDev(data, mean) {
  const variance =
    data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length;
  return Math.sqrt(variance);
}
const initChart = () => {
  if (!chart) return;
  chart.clear();

  let option: EChartsOption;

  // 检查 xLine 是否包含非数字数据
  const invalidValues = props.xLine.filter(value => isNaN(value));
  if (invalidValues.length > 0) {
    return; // 如果存在无效数据，则停止执行
  }
  // 转为数字类型
  const xLineNumber = props.xLine.map(value => Number(value));
  // 计算每个点的频率（出现次数）
  const frequencyMap = new Map();
  xLineNumber.forEach((value: number) => {
    frequencyMap.set(value, (frequencyMap.get(value) || 0) + 1);
  });

  // 从 frequencyMap 获取 x 轴的唯一值（从小到大排序）
  const uniqueX = Array.from(frequencyMap.keys()).sort((a, b) => a - b);

  // 获取每个点的频率值（存入 frequencyY）
  const frequencyY = uniqueX.map((x: number) => frequencyMap.get(x));

  // 检查 uniqueX 是否为空
  if (uniqueX.length === 0) {
    return; // 如果为空，停止执行
  }
  // 确保 uniqueX 中的每个值都转换为数字类型
  const uniqueXNumeric = uniqueX
    .map(value => {
      const numValue = Number(value); // 将字符串转换为数字
      if (isNaN(numValue)) {
        return NaN; // 无效值将返回 NaN
      }
      return numValue; // 有效的数字返回
    })
    .filter(value => !isNaN(value)); // 过滤掉无效值

  // 检查 uniqueXNumeric 是否为空
  if (uniqueXNumeric.length === 0) {
    return; // 如果为空，停止执行
  }

  // 计算 mean
  const mean = calculateMean(xLineNumber);

  // 计算 variance 和 stdDev
  const stdDev = calculateStdDev(xLineNumber, mean);

  // 检查 stdDev 是否为 0
  if (stdDev === 0) {
    console.warn("数据标准差为 0，正态分布可能无法绘制。");
  }

  if (isNaN(mean) || isNaN(stdDev) || stdDev === 0) {
    console.error("计算 mean 或 stdDev 时出错！请检查数据。");
    return;
  }
  console.log("uniqueXNumeric", uniqueXNumeric, "uniqueX", uniqueX);
  const numPoints = 1000;

  // 生成正态分布曲线的数据
  const minX = Math.min(...xLineNumber);
  const maxX = Math.max(...xLineNumber);
  const step = (maxX - minX) / numPoints;
  let xValues = [];
  let yValues = [];
  console.log(
    "minX",
    minX,
    "maxX",
    maxX,
    "step",
    step,
    "mean",
    mean,
    "stdDev",
    stdDev
  );
  // 正态分布的概率密度函数
  function normalPDF(x, mean, stdDev) {
    const exponent = -Math.pow(x - mean, 2) / (2 * Math.pow(stdDev, 2));
    return (1 / (stdDev * Math.sqrt(2 * Math.PI))) * Math.exp(exponent);
  }

  for (let i = 0; i < numPoints; i++) {
    const x = minX + i * step;
    const y = normalPDF(x, mean, stdDev);
    xValues.push(x);
    yValues.push(y);
  }
  console.log(xValues, "xValues", yValues, "yValues");
  option = {
    title: {
      text: props.title, // 标题内容
      left: "center", // 将标题居中
      //top: "2%", // 距顶部距离
      //bottom: "2%", //底部距离
      textStyle: {
        color: "black", // 标题颜色
        fontSize: 18, // 标题字体大小
        fontWeight: "bold", // 加粗字体
        fontStyle: "normal", // 正楷
        fontFamily: "Arial, sans-serif", // 字体
        textBorderColor: "rgba(255, 255, 255, 0.6)", // 标题边框颜色
        textBorderWidth: 0, // 标题边框宽度
        shadowColor: "rgba(0, 0, 0, 0.6)" // 标题阴影颜色
      },
      //backgroundColor: "rgba(245, 245, 245, 0.6)", // 标题背景色（带透明度）
      borderRadius: 10, // 圆角背景
      padding: [10, 15], // 内边距
      borderColor: "rgba(255, 255, 255, 0.6)", // 边框颜色
      borderWidth: 1 // 边框宽度
    },
    backgroundColor: "#fff", // 设置图表背景颜色为白色
    // graphic: {
    //   type: "text",
    //   left: "center",
    //   top: "middle",
    //   silent: true,
    //   invisible: props.yLine.length > 0,
    //   style: {
    //     fill: "#9d9d9d",
    //     fontWeight: "bold",
    //     text: "No Data",
    //     fontFamily: "Microsoft YaHei",
    //     fontSize: 25
    //   }
    // },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)", // 背景透明度
      borderColor: "#333", // 边框颜色
      borderWidth: 1,
      textStyle: {
        color: "#fff", // 字体颜色
        fontSize: 14 // 字体大小
      },
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(0,0,0,0.1)" // 指示线的阴影效果
        }
      }
    },

    legend: {
      top: "8%",
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      itemGap: 10 // 图例项之间的间距
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    grid: {
      top: "20%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: uniqueXNumeric // 使用转换后的数字类型数据
      },
      {
        type: "category",
        data: xValues, // 使用扩展的 1000 个点来绘制正态分布曲线
        show: false // 隐藏正态分布曲线的 x 轴
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        name: "Frequency", // 左侧Y轴显示频率
        position: "left"
      },
      {
        type: "value",
        scale: true,
        name: "Normal Distribution", // 右侧Y轴显示正态分布
        position: "right"
      }
    ],

    // dataZoom: [
    //   //滚动条
    //   {
    //     type: "slider",
    //     realtime: true,
    //     xAxisIndex: [0],
    //     bottom: "10",
    //     height: 15,
    //     handleSize: "150%", // 增加手柄的大小
    //     handleStyle: {
    //       color: "#fff", // 手柄的颜色
    //       borderColor: "#5D92F4" // 手柄的边框颜色
    //     },
    //     textStyle: {
    //       color: "#5D92F4" // 滚动条上的文字颜色
    //     },
    //     borderColor: "#5D92F4", // 滚动条边框颜色
    //     fillerColor: "rgba(93, 146, 244, 0.5)", // 选中区域的颜色
    //     backgroundColor: "rgba(221, 221, 221, 0.2)" // 未选中区域的背景色
    //   }
    // ],
    series: [
      // 绘制频率数据（直方图）
      {
        name: "Frequency",
        type: "bar",
        data: frequencyY, // 使用计算的频率值
        itemStyle: {
          color: "#6FA3D8"
        },
        yAxisIndex: 0, // 绑定到左侧 Y 轴
        xAxisIndex: 0
      },
      // 绘制正态分布拟合曲线
      {
        name: "Normal Distribution",
        type: "line",
        data: yValues, // 使用计算的正态分布数据
        smooth: true,
        lineStyle: {
          color: "#FF0000", // 设置线条颜色为红色
          width: 2
        },
        yAxisIndex: 1, // 绑定到右侧 Y 轴
        xAxisIndex: 1
      }
    ]
  };
  option && chart.setOption(option);
};

onMounted(() => {
  const chartElement = document.getElementById(props.chartId) as HTMLElement;
  if (chartElement) {
    chart = echarts.init(chartElement);
    initChart();
  }
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine
  }),
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div :id="chartId" class="chart-container" />
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 这里可以根据需要调整高度 */
}
</style>
