import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/ics/auto",
  ICS_Trend = "/trend/data",
  GROUP_TREND = "/trend/group",
  ICS_Hot_Laser = "/hotlaser/data"
}

class IcsTrend extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getICSTrendData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.ICS_Trend, query);
  }

  getICSTrendGroup<T>(): Promise<T> {
    return this.get<T>(API.GROUP_TREND);
  }

  getICSHotLaser<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.ICS_Hot_Laser, query);
  }
}

export const icsTrend = new IcsTrend();
