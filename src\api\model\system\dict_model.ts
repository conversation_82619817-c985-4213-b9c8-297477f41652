import { BaseClass, BaseQuery } from "../domain";

export interface DictQuery extends BaseQuery {
  name?: string;
  type?: string;
}
export interface Dict extends BaseClass {
  id: string;
  description: string;
  name: string;
  type: string;
  entry: DictEntry[];
}

export interface DictEntryQuery extends BaseQuery {
  parentId?: string;
}
export interface DictEntry extends BaseClass {
  id: string;
  parentId: string;
  description: string;
  name: string;
  value: string;
  sort: number;
  isSave: boolean;
}

export interface DictCache {
  type: string;
  entry: DictEntryCache[];
}

export interface DictEntryCache {
  label: string;
  value: string;
}
