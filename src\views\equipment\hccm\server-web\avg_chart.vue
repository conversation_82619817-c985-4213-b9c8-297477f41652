<script setup lang="ts">
import { onMounted, watch } from "vue";
import * as echarts from "echarts";

type EChartsOption = echarts.EChartsOption;

const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array,
    required: true
  },
  yLine: {
    type: Array as () => Array<number>,
    required: true
  },
  title: {
    type: String,
    required: true
  }
});
let yAxisMin = 0;
let yAxisMax = 0;

const init = (xLine: Array<string>, yLine: Array<object>) => {
  let myChart = echarts.init(document.getElementById(props.chartId));
  myChart.clear();
  let option: EChartsOption;
  option = {
    title: {
      text: props.title, // 标题内容
      left: "center", // 将标题居中
      //top: "2%", // 距顶部距离
      //bottom: "2%", //底部距离
      textStyle: {
        color: "black", // 标题颜色
        fontSize: 18, // 标题字体大小
        fontWeight: "bold", // 加粗字体
        fontStyle: "normal", // 正楷
        fontFamily: "Arial, sans-serif", // 字体
        textBorderColor: "rgba(255, 255, 255, 0.6)", // 标题边框颜色
        textBorderWidth: 0, // 标题边框宽度
        shadowColor: "rgba(0, 0, 0, 0.6)" // 标题阴影颜色
      },
      //backgroundColor: "rgba(245, 245, 245, 0.6)", // 标题背景色（带透明度）
      borderRadius: 10, // 圆角背景
      padding: [10, 15], // 内边距
      borderColor: "rgba(255, 255, 255, 0.6)", // 边框颜色
      borderWidth: 1 // 边框宽度
    },
    backgroundColor: "#fff", // 设置图表背景颜色为白色
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: props.yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(50, 50, 50, 0.8)", // 背景透明度
      borderColor: "#333", // 边框颜色
      borderWidth: 1,
      textStyle: {
        color: "#fff", // 字体颜色
        fontSize: 14 // 字体大小
      },
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(0,0,0,0.1)" // 指示线的阴影效果
        }
      }
    },

    legend: {
      top: "8%",
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      itemGap: 10 // 图例项之间的间距
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    grid: {
      top: "20%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: xLine
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        min: yAxisMin == 0 ? null : yAxisMin,
        max: yAxisMax == 0 ? null : yAxisMax
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 10,
        borderColor: "rgba(0,0,0,0)",
        textStyle: {
          color: "#05D5FF"
        }
      }
    ],
    series: yLine
  };
  option && myChart.setOption(option);
};

defineOptions({
  name: "icsTxtStatisticLine"
});

onMounted(() => {
  getData();
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine
  }),
  () => {
    getData();
  },
  { deep: true }
);
const getData = () => {
  let xAxisData = props.xLine as string[];
  let seriesData = [];

  const mean =
    props.yLine.reduce((sum, x) => sum + parseFloat(x), 0) / props.yLine.length;

  const variance =
    props.yLine.map(x => (x - mean) ** 2).reduce((sum, x) => sum + x, 0) /
    (props.yLine.length - 1);
  const standardDeviation = Math.sqrt(variance);
  const UCL = mean + 3 * standardDeviation;
  const LCL = mean - 3 * standardDeviation;

  let oneCopy = props.yLine.slice();
  oneCopy.push(parseFloat(UCL.toFixed(2)));
  oneCopy.push(parseFloat(LCL.toFixed(2)));

  let markLineData: any = {
    symbol: ["none", "none"],
    data: [
      {
        name: "Mean",
        type: "average",
        lineStyle: {
          type: "dashed",
          width: 2
        },
        label: {
          fontSize: 12,
          fontWeight: "bolder",
          color: "black",
          position: "middle",
          formatter: "{b}={c}"
        },
        zlevel: 5
      },
      {
        name: "UCL",
        yAxis: UCL,
        lineStyle: {
          color: "orange",
          type: "solid",
          width: 2
        },
        label: {
          formatter: "UCL",
          fontSize: 12,
          fontWeight: "bolder",
          color: "orange"
        },
        zlevel: 5
      },
      {
        name: "LCL",
        yAxis: LCL,
        lineStyle: {
          color: "orange",
          type: "solid",
          width: 2
        },
        label: {
          formatter: "LCL",
          fontSize: 12,
          fontWeight: "bolder",
          color: "orange"
        },
        zlevel: 5
      }
    ]
  };
  oneCopy.sort((a, b) => {
    return a < b ? -1 : a > b ? 1 : 0;
  });
  yAxisMin = oneCopy[0];
  yAxisMax = oneCopy[oneCopy.length - 1];

  var svoOne: any = {
    name: props.title,
    type: "line",
    stack: "oneLine",
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: {
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    symbol: "circle",
    symbolSize: 5,
    markLine: markLineData,
    itemStyle: {
      color: function (params) {
        if (
          props.yLine[params.dataIndex] > parseFloat(UCL.toFixed(2)) ||
          props.yLine[params.dataIndex] < parseFloat(LCL.toFixed(2))
        ) {
          return "orange";
        } else {
          return "blue";
        }
      }
    },
    data: props.yLine,
    zlevel: 3
  };

  seriesData.push(svoOne);

  init(xAxisData, seriesData);
};
</script>

<template>
  <div>
    <div :id="chartId" style="width: 100%; height: 400px" />
  </div>
</template>

<style scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  position: fixed;
  z-index: 3000;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
}

.contextmenu li:hover {
  background: #eee;
}
</style>
