<script setup lang="ts">
import { userEquipmentAPI } from "/@/api/equipment/userEquipment";
import { onMounted, reactive } from "vue";
import {
  Equipment,
  EquipmentQuery,
  EquipmentJarId
} from "/@/api/model/equipment/user_equipment_model";
import { Page } from "/@/api/model/domain";
import addIndex from "./addIndex.vue";
import updateIndex from "./updateIndex.vue";
import { ElMessageBox } from "element-plus";

defineOptions({
  name: "EquipmentManage"
});

const pageData = reactive<{
  loading: boolean;
  selection: Equipment[];
  testList: Equipment[];
  searchTestInfo: EquipmentQuery;
  addIndexPage: boolean;
  updateIndexPage: boolean;
  JarDetailList: EquipmentJarId[];
  updateEntity: Equipment;
}>({
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    equipmentName: "",
    equipmentType: "",
    jarId: "",
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  },
  addIndexPage: false,
  updateIndexPage: false,
  JarDetailList: [],
  updateEntity: null
});

const getPage = () => {
  pageData.loading = true;
  userEquipmentAPI
    .page(pageData.searchTestInfo)
    .then((res: Page<Equipment[]> | string) => {
      if (res !== "fail") {
        pageData.testList = (res as Page<Equipment[]>).records;
        pageData.searchTestInfo.total = Number(
          (res as Page<Equipment[]>).total
        );
      }
    })
    .finally(() => (pageData.loading = false));
};

const getJarIdList = () => {
  userEquipmentAPI.getJarIdList().then((res: EquipmentJarId[] | string) => {
    if (res !== "fail") {
      pageData.JarDetailList = res as EquipmentJarId[];
    }
  });
};

onMounted(() => {
  getPage();
  getJarIdList();
});

const handlerSearch = () => {
  pageData.searchTestInfo.pageNum = 1;
  getPage();
};

const handelSelectionChange = val => {
  pageData.selection = val;
};

const sizeChange = (pageSize: number) => {
  pageData.searchTestInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchTestInfo.pageNum = pageNum;
  getPage();
};

const openAddIndexPage = () => {
  pageData.addIndexPage = true;
};

const closeAddIndexPage = () => {
  pageData.addIndexPage = false;
  getPage();
};

const openUpdateIndexPage = (vo: Equipment) => {
  pageData.updateEntity = vo;
  pageData.updateIndexPage = true;
};

const closeUpdateIndexPage = () => {
  pageData.updateIndexPage = false;
  getPage();
};

const deleteBatch = () => {
  if (pageData.selection.length > 0) {
    let ids: string[] = [];
    pageData.selection.forEach(item => ids.push(item.id));
    ElMessageBox.confirm("Are you delete batch?")
      .then(() => {
        userEquipmentAPI.deleteBatch(ids).then(() => {
          getPage();
        });
      })
      .catch(() => {
        // catch error
      });
  }
};

const deleteById = (id: string) => {
  ElMessageBox.confirm("Are you delete batch?")
    .then(() => {
      userEquipmentAPI.deleteById(id).then(() => {
        getPage();
      });
    })
    .catch(() => {
      // catch error
    });
};

const getEquipmentType = data => {
  const type = data.equipmentType;
  const etn = userEquipmentAPI.EquipmentType;
  for (const item in etn) {
    if (etn[item].key == type) {
      return etn[item].value;
    }
  }
  return null;
};
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-input
          v-model="pageData.searchTestInfo.equipmentName"
          placeholder="设备名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.equipmentType"
          placeholder="设备类型"
        >
          <el-option
            v-for="item in userEquipmentAPI.EquipmentType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="pageData.searchTestInfo.jarId" placeholder="生产线">
          <el-option
            v-for="item in pageData.JarDetailList"
            :key="item.id"
            :label="item.jarId"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="handlerSearch">Query</el-button>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="openAddIndexPage">Add Equipment</el-button>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="deleteBatch">Delete batch</el-button>
      </el-form-item>
      <add-index
        v-if="pageData.addIndexPage"
        :closeCallBack="closeAddIndexPage"
      />
      <update-index
        v-if="pageData.updateIndexPage"
        :closeCallBack="closeUpdateIndexPage"
        :entity="pageData.updateEntity"
      />
    </el-form>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        @selection-change="handelSelectionChange"
      >
        <el-table-column
          sortable
          resizable
          :show-overflow-tooltip="true"
          type="selection"
        />
        <el-table-column
          prop="equipmentName"
          label="设备名称"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="equipmentType"
          label="设备类型"
          sortable
          resizable
          :show-overflow-tooltip="true"
          :formatter="getEquipmentType"
        />
        <el-table-column
          prop="jarId"
          label="生产线"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="address"
          label="业务地址"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserDomain"
          label="账号分组"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserAccount"
          label="账号"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="fileUserPws"
          label="密码"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />

        <el-table-column fixed="right" label="Operations" width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="openUpdateIndexPage(scope.row)"
              >update</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="deleteById(scope.row.id)"
              >delete</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchTestInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchTestInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchTestInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped></style>
