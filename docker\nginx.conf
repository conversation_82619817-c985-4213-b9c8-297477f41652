worker_processes 1;

events {
  worker_connections 1024;
}

http {

  include         mime.types;
  default_type    application/octet-stream;

  sendfile    on;

  keepalive_timeout    65;

  upstream apibackend {
    server 127.0.0.1:8080;
    server 10.10.200.188:8080;
  }

  server {
    listen        80;
    server_name   10.10.200.172;

    location / {
      alias       /app/dist/;
      index       index.html index.htm;
      try_files   $uri $uri/ /index.html;
    }

    location ~ /api/ {
      proxy_pass  http://apibackend;
      rewrite     "^/api/(.*)$" /$1 break;
    }

  }
}