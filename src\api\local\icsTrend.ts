import { http } from "/@/utils/http";

class IcsTrendAPI {
  getabInfo(): any {
    return http.request(
      "get",
      "http://localhost:8080/client/ics/trend/info",
      null
    );
  }

  getPosData(params): any {
    return http.request("get", "http://localhost:8080/client/ics/trend/data", {
      params
    });
  }

  getHotLaserData(params): any {
    return http.request(
      "get",
      "http://localhost:8080/client/ics/hotlaser/data",
      {
        params
      }
    );
  }
}

export const icsTrendAPI = new IcsTrendAPI();
