<script setup lang="ts">
import { onBeforeMount, ref, watch } from "vue";
import { ivsPosAPIs } from "/@/api/ivs/ivsPos";
import Chart from "./chart.vue"; // Import chart component
import { dayjs } from "element-plus";

// 添加日期范围
const dateRange = ref();

// 根据按钮类型设置默认日期范围
const setDefaultDateRange = (type: string) => {
  const endDate = dayjs().format("YYYY-MM-DD"); // 获取当前日期
  let startDate; // 默认前一天

  // 根据不同类型设置不同的日期范围
  if (type === "4") {
    // Monthly
    startDate = dayjs().subtract(60, "day").format("YYYY-MM-DD"); // 默认前一天
  } else {
    // Shift, Daily, Weekly
    startDate = dayjs().subtract(15, "day").format("YYYY-MM-DD"); // 默认前一天
  }

  dateRange.value = [startDate, endDate];
};
const isCollapsed = ref(false);
// 响应式阈值对象
const thresholds = ref({
  yield: 95,
  defect_trend: 3,
  defect_by_tray_position: 5,
  defect_by_hole_number: 2
});
// Reactive variables for storing different chart data
const chartDataDefctL = ref<any[]>([]);
const chartDataDefctR = ref<any[]>([]);
const chartDataPadYield = ref<any[]>([]);

const xAxisDataL = ref<string[]>([]);
const xAxisDataR = ref<string[]>([]);
const xAxisDataYield = ref<any[]>([]);
const titleName = ref("");
const selectButton = ref("1");

interface SearchInfo {
  groupId: string;
  groupName: string;
}

const props = defineProps<{
  groupList: Array<{ groupId: string; groupName: string }>;
  searchInfo: SearchInfo;
}>();

// Get data function, passing time range as parameters
const fetchData = async (type: string) => {
  // 格式化日期为 'YYYY-MM-DD'
  const startDate = dayjs(dateRange.value[0]).format("YYYY-MM-DD");
  const endDate = dayjs(dateRange.value[1]).format("YYYY-MM-DD");
  ivsPosAPIs
    .getAllData({
      type,
      groupId: props.searchInfo.groupId,
      start_time: startDate,
      end_time: endDate
    })
    .then((res: any) => {
      if (res !== "fail") {
        processIVSChartData(res);
      }
    });
};

// Data processing functions
const processIVSChartData = (res: any) => {
  chartDataDefctL.value = getSeriesFromResData(res[titleName.value + "A"]);
  chartDataDefctR.value = getSeriesFromResData(res[titleName.value + "B"]);
  chartDataPadYield.value = getYieldSeries(res);
  xAxisDataL.value = getxAxisData(res[titleName.value + "A"]);
  xAxisDataR.value = getxAxisData(res[titleName.value + "B"]);
};

const getxAxisData = (xAxis: any) => {
  return xAxis["xAxis"];
};
const getYieldSeries = (
  data: Record<
    string,
    { xAxis: string[]; yield: string[]; all: string[]; ok: string[] }
  >
) => {
  // 获取所有日期的并集
  xAxisDataYield.value = Array.from(
    new Set(Object.values(data).flatMap(item => item.xAxis))
  ).sort((a, b) => {
    // 检查并提取日期部分和类型部分
    const hasTypeA = a.endsWith("MS") || a.endsWith("ES");
    const hasTypeB = b.endsWith("MS") || b.endsWith("ES");

    // 正确提取日期和类型
    const dateA = hasTypeA ? a.slice(0, -2) : a;
    const typeA = hasTypeA ? a.slice(-2) : "";
    const dateB = hasTypeB ? b.slice(0, -2) : b;
    const typeB = hasTypeB ? b.slice(-2) : "";

    // 先按日期排序 - 转换为日期对象比较更准确
    const dateObjA = new Date(dateA);
    const dateObjB = new Date(dateB);
    const dateCompare = dateObjA.getTime() - dateObjB.getTime();
    if (dateCompare !== 0) return dateCompare;

    // 同一天时，空类型排最后，MS排在ES前
    const typeOrder = { MS: 0, ES: 1, "": 2 };
    return (typeOrder[typeA] ?? 3) - (typeOrder[typeB] ?? 3);
  });
  console.log("xAxisDataYield", xAxisDataYield.value);
  // 构建系列数据
  const series = Object.keys(data).map(key => {
    const seriesData = xAxisDataYield.value.map(date => {
      const index = data[key].xAxis.indexOf(date);
      return index !== -1 ? parseFloat(data[key].yield[index]) : null;
    });
    return {
      name: key,
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: seriesData
    };
  });
  // 计算总体良率
  const totalAll = new Array(xAxisDataYield.value.length).fill(0);
  const totalOk = new Array(xAxisDataYield.value.length).fill(0);

  Object.keys(data).forEach(key => {
    const dataMap = new Map();

    Object.keys(data[key]).forEach(itemKey => {
      if (itemKey === "xAxis") {
        const item = data[key][itemKey];
        item.forEach((x: string, index: number) => {
          dataMap.set(x, {
            all:
              data[key].all[index] === undefined ? "0" : data[key].all[index],
            ok: data[key].ok[index] === undefined ? "0" : data[key].ok[index]
          });
        });
      }
    });
    //console.log("dataMap", dataMap);
    xAxisDataYield.value.forEach((x, index) => {
      const item = dataMap.get(x);
      if (item) {
        totalAll[index] += Number(item.all);
        totalOk[index] += Number(item.ok);
      }
    });
  });
  // console.log("totalAll", totalAll);
  // console.log("totalOk", totalOk);
  // 计算总体良率数据
  const totalYieldData = totalAll.map((all, index) => {
    return all > 0
      ? parseFloat(((totalOk[index] / all) * 100).toFixed(2))
      : null;
  });
  //console.log("totalYieldData", totalYieldData);
  //添加总体良率线
  series.push({
    name: "all",
    type: "line",
    stack: "all", // Add the missing stack property
    data: totalYieldData,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null
  });

  return series;
};

// Helper function for chart data formatting
const getSeriesFromResData = (series: any) => {
  const seriesData: any[] = [];
  if (series) {
    Object.keys(series).forEach(key => {
      if (key === "xAxis" || key === "yield" || key === "ok" || key === "all") {
        return;
      }
      seriesData.push({
        name: key.toUpperCase(),
        type: "line",
        stack: key,
        yAxisIndex: 0,
        emphasis: {
          focus: "series"
        },
        markPoint: null,
        markLine: null,
        data: series[key] || []
      });
    });
  }
  return seriesData;
};

const getType = (type: string) => {
  selectButton.value = type;
  // 设置默认日期范围
  setDefaultDateRange(type);
  fetchData(type);
};

onBeforeMount(() => {
  setTimeout(() => {
    loadSettings();
    titleName.value = props.searchInfo.groupName;
    getType(selectButton.value);
  }, 50);
});

//监听groupId变化
watch(
  () => props.searchInfo.groupId,
  () => {
    setTimeout(() => {
      const groupNames = props.groupList
        .find(g => g.groupId === props.searchInfo.groupId)
        .groupName.split("_");
      //props.searchInfo.groupName = groupNames[groupNames.length - 1];
      titleName.value = groupNames[groupNames.length - 1];
      fetchData(selectButton.value);
    }, 50);
  }
);

// 新增：保存设置到数据库
const updateThreshold = async (type: string) => {
  const value = thresholds.value[type];
  // 保存到数据库
  ivsPosAPIs.updateAlarmPercent({ name: type, value }).then((res: any) => {
    if (res !== "fail") {
      loadSettings();
    }
  });
};
// 新增：从数据库加载设置
const loadSettings = async () => {
  ivsPosAPIs.getConfInfo().then((res: any) => {
    if (res !== "fail") {
      res.forEach(item => {
        console.log(Number(item.confValue));
        thresholds.value[item.confName] = Number(item.confValue);
      });
    }
  });
};
</script>

<template>
  <div>
    <div>
      <!-- Buttons for Shift and Time Range Selection -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="120px" class="filter-form">
            <!-- Time Range Buttons -->
            <el-form-item>
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '1' }"
                @click="getType('1')"
                >Shift</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '2' }"
                @click="getType('2')"
                >Daily</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '3' }"
                @click="getType('3')"
                >SAE Weekly</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '4' }"
                @click="getType('4')"
                >Monthly</el-button
              >
              <!-- 新增日期选择器 -->
              <el-col :span="8">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="to"
                  start-placeholder="start"
                  end-placeholder="end"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="date-picker"
                  @change="fetchData(selectButton)"
                />
              </el-col>
            </el-form-item>
            <!-- Date Picker -->
          </el-form>
        </el-col>
      </el-row>
      <div>
        <!-- 折叠按钮 -->
        <el-row class="collapse-btn-row">
          <el-col :span="24" style="text-align: left; margin-bottom: 10px">
            <el-button
              @click="isCollapsed = !isCollapsed"
              type="primary"
              size="small"
            >
              {{ isCollapsed ? "SHOW TABLE" : "HIDE TABLE" }}
            </el-button>
          </el-col>
        </el-row>

        <!-- 可折叠的表格区域 -->
        <div v-show="!isCollapsed">
          <el-row :gutter="20" class="settings-container">
            <!-- 第一行 -->
            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="BARI IVS Yield">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.yield"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('yield')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="IVS Defect Threshold">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_trend"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('defect_trend')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <!-- 第二行 -->
            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="T Defect By Tray Position">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_by_tray_position"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      v-auth="['icsYield:update']"
                      @click="updateThreshold('defect_by_tray_position')"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="T-Defect By Nozzle Hole">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_by_hole_number"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('defect_by_hole_number')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-chart-yield"
            :xLine="xAxisDataYield"
            :yLine="chartDataPadYield"
            :alarmLine="thresholds.yield"
            title="BARI IVS Yield"
          />
        </el-col>
      </el-row>

      <!-- Render four charts -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-l-defct"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            :alarmLine="thresholds.defect_trend"
            :title="titleName + ' L-IVS T defct trend'"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar-defct"
            :xLine="xAxisDataR"
            :yLine="chartDataDefctR"
            :alarmLine="thresholds.defect_trend"
            :title="titleName + ' R-IVS T defct trend'"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .settings-row {
    padding: 12px;
  }

  .el-col {
    margin-bottom: 8px;
  }

  .button-col {
    text-align: center;
    margin-top: 16px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .setting-item {
    flex: 1 1 100%;
  }

  .input-row {
    gap: 8px;
  }

  .action-btn {
    width: 70px;
    padding: 0 10px;
  }
}

.settings-row {
  margin-bottom: 20px;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.compact-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #606266;
    padding-bottom: 4px;
  }
}

.compact-input {
  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}

.button-col {
  text-align: left;
  margin-top: 12px;
}

.submit-btn {
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.chart-row {
  margin-bottom: 20px;
  margin-top: 16px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
}

.settings-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.setting-item {
  flex: 1 1 calc(50% - 20px);
  min-width: 300px;
}

.setting-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.input-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.el-input {
  flex: 1;
  min-width: 120px;
}

.action-btn {
  flex-shrink: 0;
  width: 80px;
  height: 32px;
  padding: 0 12px;
}

.el-form-item__label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  height: 32px;
  padding: 0 15px;
} /* 响应式调整 */
</style>
