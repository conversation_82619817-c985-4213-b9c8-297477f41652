import BaseRequest from "../base";
import { getFileListParam } from "/@/api/model/equipment/ics_txt_statistic";
enum API {
  BASE_URL = "/icsTxtStatistics"
}

class IcsTxtStatisticAPI extends BaseRequest {
  private getData: string;
  private getEquipList: string;
  private getChartDataUrl: string;
  private getFileListUrl: string;

  constructor() {
    super();
    this.getData = "/list";
    this.getEquipList = "/getEquipmentSelectList";
    this.getChartDataUrl = "/getChartByCollectTime";
    this.getFileListUrl = "/getFileListByTime";
  }

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  list<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getData, query);
  }

  getChartData<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getChartDataUrl, query);
  }

  getEquipmentSelectList<T>(): Promise<T> {
    return this.get<T>(this.getEquipList);
  }

  getFileListByTime<T>(query: getFileListParam): Promise<T> {
    return this.get<T>(this.getFileListUrl, query);
  }
}

export const icsTxtStatisticAPI = new IcsTxtStatisticAPI();
