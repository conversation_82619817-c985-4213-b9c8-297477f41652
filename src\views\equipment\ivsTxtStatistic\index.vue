<script setup lang="ts">
import { ivsTxtStatisticAPI } from "/@/api/equipment/ivsTxtStatistic";
import { onMounted, reactive } from "vue";
import {
  paramList,
  ivsTxtVO,
  IvsEquipmentVO
} from "/@/api/model/equipment/ivs_txt_statistic";
import Line from "./Line.vue";
import Query from "./query.vue";

defineOptions({
  name: "ivsTxtStatistic"
});

const pageData = reactive<{
  componentKey: number;
  line: boolean;
  loading: boolean;
  selection: any[];
  testList: ivsTxtVO[];
  searchTestInfo: paramList;
  IvsEquipmentList: IvsEquipmentVO[];
  padResultList: Array<{ name: string; value: string }>;
  dates: string;
}>({
  componentKey: 1,
  line: true,
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    start: "",
    end: "",
    equipmentId: null,
    padResult: ""
  },
  IvsEquipmentList: [],
  padResultList: [
    { name: "请选择", value: "" },
    { name: "左", value: "L" },
    { name: "右", value: "R" }
  ],
  dates: ""
});

const shortcuts = [
  {
    text: "Last week",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "Last month",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "Last 3 months",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

const getPage = () => {
  pageData.loading = true;
  pageData.line = false;
  ivsTxtStatisticAPI
    .list(pageData.searchTestInfo)
    .then((res: ivsTxtVO[] | string) => {
      if (res !== "fail") {
        pageData.testList = res as ivsTxtVO[];
      }
    })
    .finally(() => {
      pageData.loading = false;
      pageData.line = true;
      pageData.componentKey += 1;
    });
};

let getEquipmentSelectList = () => {
  ivsTxtStatisticAPI
    .getEquipmentSelectList()
    .then((res: IvsEquipmentVO[] | string) => {
      if (res !== "fail") {
        pageData.IvsEquipmentList = res as IvsEquipmentVO[];
        pageData.searchTestInfo.equipmentId = pageData.IvsEquipmentList[0].id;
        getPage();
      }
    });
};

const getTodayDate = (date: Date) => {
  let month: string | number = date.getMonth() + 1;
  let strDate: string | number = date.getDate();

  if (month <= 9) {
    month = "0" + month;
  }

  if (strDate <= 9) {
    strDate = "0" + strDate;
  }

  return date.getFullYear() + "-" + month + "-" + strDate;
};

onMounted(() => {
  pageData.searchTestInfo.start = getTodayDate(new Date());
  pageData.searchTestInfo.end = pageData.searchTestInfo.start;
  getEquipmentSelectList();
});

const handlerSearch = () => {
  console.log(pageData.dates);
  if (pageData.dates != null && pageData.dates != "") {
    pageData.searchTestInfo.start = pageData.dates[0];
    pageData.searchTestInfo.end = pageData.dates[1];
  }
  getPage();
};

const handelSelectionChange = val => {
  pageData.selection = val;
};
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.equipmentId"
          placeholder="设备"
        >
          <el-option
            v-for="item in pageData.IvsEquipmentList"
            :key="item.id"
            :label="item.equipmentName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="pageData.dates"
          type="daterange"
          unlink-panels
          range-separator="To"
          :startPlaceholder="pageData.searchTestInfo.start"
          :endPlaceholder="pageData.searchTestInfo.end"
          value-format="YYYY-MM-DD"
          :default-value="[new Date(), new Date()]"
          :shortcuts="shortcuts"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.padResult"
          placeholder="左右"
        >
          <el-option
            v-for="item in pageData.padResultList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="handlerSearch">Query</el-button>
      </el-form-item>
    </el-form>

    <el-card>
      <template #default>
        <Line :txt="pageData.testList" v-if="pageData.line" />
      </template>
    </el-card>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        @selection-change="handelSelectionChange"
      >
        <el-table-column
          sortable
          resizable
          :show-overflow-tooltip="true"
          type="selection"
        />
        <el-table-column
          prop="result"
          label="异常分类"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result0"
          label="P1"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result1"
          label="P2"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result2"
          label="P3"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result3"
          label="P4"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result4"
          label="P5"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result5"
          label="P6"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result6"
          label="P7"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result7"
          label="P8"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result8"
          label="P9"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result9"
          label="P10"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
      </el-table>
    </el-card>
    <el-card>
      <template #default>
        <Query :txt="pageData.searchTestInfo" :key="pageData.componentKey" />
      </template>
    </el-card>
  </div>
</template>

<style scoped></style>
