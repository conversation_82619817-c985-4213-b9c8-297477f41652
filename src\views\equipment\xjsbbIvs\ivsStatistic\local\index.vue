<script setup lang="ts">
import { computed, onBeforeMount, ref } from "vue";
import { iVSPosAPI } from "/@/api/local/ivsPos";
import Chart from "./chart.vue"; // Import chart component
import { ElCard, ElTable, ElTableColumn } from "element-plus";
import days from "dayjs";

const isCollapsed = ref(false);

// 新增的表格数据
// 四个表格的阈值
const thresholds = ref({
  yield: 95, // BARI IVS Yield 阈值
  defect_trend: 3, // IVS defect 阈值
  defect_by_tray_position: 5, // T defct trend 阈值
  defect_by_hole_number: 2 // T-defect 阈值
});
// 新增表格数据和阈值
const tableDataYield = ref<{ type: string; value: number }[]>([]);
const tableDataTDefctTrend = ref<Array<Record<string, any>>>([]);
const tableDataIvsDefect = ref<Array<Record<string, any>>>([]);
const tableDataTDefect = ref<Array<Record<string, any>>>([]);

// Reactive variables for storing different chart data
const chartDataDefctL = ref<any[]>([]);
const chartDataDefctR = ref<any[]>([]);
const chartDataPadYield = ref<any[]>([]);

const xAxisDataL = ref<string[]>([]);
const xAxisDataR = ref<string[]>([]);

const selectButton = ref("1");

// Get data function, passing time range as parameters
const fetchData = async (type: string, startDateTime, endDateTime) => {
  iVSPosAPI.getAllData({ type }).then((res: any) => {
    if (res !== "fail") {
      processIVSChartData(res, startDateTime, endDateTime);
    }
  });
};

// Data processing functions
const processIVSChartData = (res: any, startDateTime, endDateTime) => {
  chartDataDefctL.value = getSeriesFromResData("L-Defct", res["L-IVS-ALL"]);
  chartDataDefctR.value = getSeriesFromResData("R-Defct", res["R-IVS-ALL"]);
  chartDataPadYield.value = getYieldSeries(
    "L",
    res["L-IVS-ALL"],
    "R",
    res["R-IVS-ALL"]
  );
  xAxisDataL.value = getxAxisData(res["L-IVS-ALL"]);
  xAxisDataR.value = getxAxisData(res["R-IVS-ALL"]);
  iVSPosAPI.getIVSAlarmPercent().then((res: any) => {
    if (res !== "fail") {
      thresholds.value = res;
      // 新增表格数据处理
      processYieldData();
      processIvsDefectData();
      iVSPosAPI
        .getIVSData({ start_time: startDateTime, end_time: endDateTime })
        .then((res: any) => {
          // res = {
          //   "L-IVS": ["1", "0", "2", "0", "0", "0", "0", "0", "0", "0"],
          //   "R-IVS": ["1", "0", "2", "0", "0", "0", "0", "0", "0", "0"]
          // };
          if (res !== "fail") {
            processTDefctTrendData(res);
          }
        });
      iVSPosAPI
        .getPadData({ start_time: startDateTime, end_time: endDateTime })
        .then((res: any) => {
          // res = {
          //   "R-PAD": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 30],
          //   "L-PAD": [1, 2, 3, 4, 5, 6, 7, 8, 25, 10, 11, 12, 13]
          // };
          if (res !== "fail") {
            processTDefectData(res); // 保持原有逻辑，根据需要修改
          }
        });
    }
  });
};

const getxAxisData = (xAxis: any) => {
  return xAxis["xAxis"];
};
const getYieldSeries = (
  key1: string,
  series1: any,
  key2: string,
  series2: any
) => {
  const seriesData: any[] = [];

  seriesData.push({
    name: key1,
    type: "line",
    stack: key1,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null,
    data: series1["yield"] || []
  });

  seriesData.push({
    name: key2,
    type: "line",
    stack: key2,
    yAxisIndex: 0,
    emphasis: {
      focus: "series"
    },
    markPoint: null,
    markLine: null,
    data: series2["yield"] || []
  });

  return seriesData;
};
// Helper function for chart data formatting
const getSeriesFromResData = (chartName: string, series: any) => {
  const seriesData: any[] = [];
  Object.keys(series).forEach(key => {
    if (key === "xAxis" || key === "yield") {
      return;
    }
    seriesData.push({
      name: key.toUpperCase(),
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: series[key] || []
    });
  });
  return seriesData;
};

// Disable future dates
const getType = (type: string) => {
  selectButton.value = type;
  const getDate = getNowDate();
  fetchData(type, getDate[0], getDate[1]);
};

// 数据排序函数
const sortByThreshold = (data, threshold) => {
  // 转换为带索引的对象 {原始位置: 值}
  const indexedData = data.reduce((acc, value, index) => {
    acc[index + 1] = parseFloat(value) || 0; // 位置从1开始
    return acc;
  }, {});

  // 分组处理
  const thresholdValue = Number(threshold);
  const result = {
    above: {}, // 超过阈值组
    below: {} // 未达阈值组
  };

  Object.entries(indexedData).forEach(([position, value]) => {
    if (Number(value) >= thresholdValue) {
      result.above[position] = value;
    } else {
      result.below[position] = value;
    }
  });

  // 合并结果时保留各组内部原始顺序
  return {
    sorted: { ...result.above, ...result.below },
    groups: result
  };
};

// BARI IVS Yield 数据处理（从chartDataPadYield获取）
const processYieldData = () => {
  const lSeries = chartDataPadYield.value.find(s => s.name === "L")?.data || [];
  const rSeries = chartDataPadYield.value.find(s => s.name === "R")?.data || [];
  //大于thresholds.yield的值为排前面，其他按顺序排后面

  //转成int
  tableDataYield.value = [
    { type: "L", value: parseFloat(lSeries[lSeries.length - 1]) },
    { type: "R", value: parseFloat(rSeries[lSeries.length - 1]) }
  ];
};

// IVS defect 数据处理（从chartDataDefctL/R获取）
const processIvsDefectData = () => {
  //chartDataDefctL.value按照nameT1-T7的顺序排列
  const chartDataDefctLValue = chartDataDefctL.value.sort((a, b) => {
    return a.name.localeCompare(b.name);
  });
  const chartDataDefctRValue = chartDataDefctR.value.sort((a, b) => {
    return a.name.localeCompare(b.name);
  });

  // 获取L侧最后一条数据（假设每个系列对应一个P值）
  const lDefects = chartDataDefctLValue
    .filter(s => s.name.startsWith("T"))
    .map(s => s.data[s.data.length - 1] || 0);

  // 获取R侧最后一条数据
  const rDefects = chartDataDefctRValue
    .filter(s => s.name.startsWith("T"))
    .map(s => s.data[s.data.length - 1] || 0);
  //转成float
  rDefects.map(item => {
    return parseFloat(item);
  });
  lDefects.map(item => {
    return parseFloat(item);
  });

  const l = sortByThreshold(lDefects, thresholds.value.defect_trend);
  const r = sortByThreshold(rDefects, thresholds.value.defect_trend);
  // 生成表格数据（T1-T7）
  tableDataIvsDefect.value = [
    { type: "L", ...generateDefectData2(l, "T") },
    { type: "R", ...generateDefectData2(r, "T") }
  ];
  console.log(tableDataIvsDefect.value);
};

const generateDefectData2 = (sortedData, pos) => {
  const result = {};
  //先遍历above
  Object.entries(sortedData.groups.above).forEach(([position, value]) => {
    result[`${pos}${position}`] = value;
  });
  //再遍历below
  // Object.entries(sortedData.groups.below).forEach(([position, value]) => {
  //   result[`T${position}`] = value;
  // });
  return result;
};

//计算每个位置的比例
const calculatePercentage = arr => {
  const sum = arr.reduce((total, num) => Number(total) + Number(num), 0); // 计算总和
  if (sum === 0) return arr.map(() => 0); // 防止除以零错误
  return arr.map(
    value => Number(((value / sum) * 100).toFixed(2)) // 计算占比并保留两位小数
  );
};
// 保持原有T defct trend和T-defect的处理逻辑
const processTDefctTrendData = res => {
  const l = sortByThreshold(
    calculatePercentage(res["L-IVS"]),
    thresholds.value.defect_by_tray_position
  );
  const r = sortByThreshold(
    calculatePercentage(res["R-IVS"]),
    thresholds.value.defect_by_tray_position
  );

  tableDataTDefctTrend.value = [
    {
      type: "L",
      ...generateDefectData2(l, "P")
    },
    {
      type: "R",
      ...generateDefectData2(r, "P")
    }
  ];
};
const processTDefectData = res => {
  const l = sortByThreshold(
    calculatePercentage(res["L-PAD"]),
    thresholds.value.defect_by_hole_number
  );
  const r = sortByThreshold(
    calculatePercentage(res["R-PAD"]),
    thresholds.value.defect_by_hole_number
  );
  tableDataTDefect.value = [
    {
      type: "L",
      ...generateDefectData2(l, "")
    },
    {
      type: "R",
      ...generateDefectData2(r, "")
    }
  ];
};

// 单元格样式处理
const cellStyle = (value: number, threshold: number) => {
  return value >= threshold ? { color: "red" } : undefined;
};

const cellStyle2 = (value: number, threshold: number) => {
  return value <= threshold ? { color: "red" } : undefined;
};
onBeforeMount(() => {
  isCollapsed.value = false;
  getType(selectButton.value);
});

const getNowDate = () => {
  // 获取当前时间
  let now = new Date();

  let shiftStart, shiftEnd;

  if (now.getHours() >= 7 && now.getHours() < 19) {
    // 当前为白班 7:00-19:00
    shiftStart = new Date(now.setHours(7, 0, 0, 0));
    shiftEnd = new Date(now.setHours(19, 0, 0, 0));
  } else {
    // 当前为夜班 19:00-次日 7:00
    shiftStart = new Date(
      now.getHours() < 7 ? now.setDate(now.getDate() - 1) : now
    ).setHours(19, 0, 0, 0);
    shiftEnd = new Date(
      now.getHours() < 7 ? now : now.setDate(now.getDate() + 1)
    ).setHours(7, 0, 0, 0);
  }

  //减去12小时
  shiftStart = new Date(shiftStart - 12 * 60 * 60 * 1000);
  shiftEnd = new Date(shiftEnd - 12 * 60 * 60 * 1000);
  //格式化
  shiftStart = days(shiftStart).format("YYYY-MM-DD HH:mm:ss");
  shiftEnd = days(shiftEnd).format("YYYY-MM-DD HH:mm:ss");
  return [shiftStart, shiftEnd];
};

// IvsDefect动态列
const dynamicColumns = computed(() => {
  const columns = new Set();

  // 遍历所有行，收集所有 T 开头的列
  tableDataIvsDefect.value.forEach(row => {
    Object.keys(row).forEach(key => {
      if (key.startsWith("T") && !columns.has(key)) {
        columns.add(key);
      }
    });
  });
  console.log(Array.from(columns), "columns");
  // 按 T1, T2, ..., Tn 排序
  return Array.from(columns).sort((a, b) => {
    const numA = parseInt((a as string).slice(1));
    const numB = parseInt((b as string).slice(1));
    return numA - numB;
  });
});
// TDefctTrendData动态列
const dynamicColumnsTDefctTrend = computed(() => {
  const columns = new Set();

  tableDataTDefctTrend.value.forEach(row => {
    Object.keys(row).forEach(key => {
      if (key.startsWith("P") && !columns.has(key)) {
        columns.add(key);
      }
    });
  });

  return Array.from(columns).sort((a, b) => {
    const numA = parseInt((a as string).slice(1));
    const numB = parseInt((b as string).slice(1));
    return numA - numB;
  });
});
// tableDataTDefect动态列
const dynamicColumnsTDefect = computed(() => {
  const columns = new Set();

  tableDataTDefect.value.forEach(row => {
    Object.keys(row).forEach(key => {
      if (key !== "type" && !columns.has(key)) {
        columns.add(key);
      }
    });
  });

  return Array.from(columns).sort(a => {
    const numA = parseInt(a as string);
    const numB = parseInt(a as string);
    return numA - numB;
  });
});
</script>

<template>
  <div>
    <div>
      <!-- Buttons for Shift and Time Range Selection -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <!-- Time Range Buttons -->
            <el-form-item>
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '1' }"
                @click="getType('1')"
                >Shift</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '2' }"
                @click="getType('2')"
                >Daily</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '3' }"
                @click="getType('3')"
                >SAE Weekly</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '4' }"
                @click="getType('4')"
                >Monthly</el-button
              >
            </el-form-item>
            <!-- Date Picker -->
          </el-form>
        </el-col>
      </el-row>

      <div>
        <!-- 折叠按钮 -->
        <el-row class="collapse-btn-row">
          <el-col :span="24" style="text-align: left; margin-bottom: 10px">
            <el-button
              @click="isCollapsed = !isCollapsed"
              type="primary"
              size="small"
            >
              {{ isCollapsed ? "SHOW TABLE" : "HIDE TABLE" }}
            </el-button>
          </el-col>
        </el-row>

        <!-- 可折叠的表格区域 -->
        <div v-show="!isCollapsed">
          <!-- 新增表格部分 -->
          <el-row :gutter="20" class="table-row">
            <!-- BARI IVS Yield -->
            <el-col :span="6">
              <el-card class="table-card">
                <template #header>
                  <div class="card-header">
                    BARI Ivs Yield ALARM( {{ thresholds.yield }}%)
                  </div>
                </template>
                <el-table :data="tableDataYield" border height="300">
                  <el-table-column
                    prop="type"
                    label="Type"
                    width="100"
                    align="center"
                  />
                  <el-table-column label="Yield" align="center">
                    <template #default="{ row }">
                      <span :style="cellStyle2(row.value, thresholds.yield)">
                        {{
                          row.value <= thresholds.yield
                            ? row.value.toFixed(2) + "%"
                            : "--"
                        }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
            <!-- IVS defect -->
            <el-col :span="18">
              <el-card class="table-card">
                <template #header>
                  <div class="card-header">
                    IVS ALARM BY T Defect ({{ thresholds.defect_trend }}%)
                  </div>
                </template>
                <el-table
                  :data="tableDataIvsDefect"
                  border
                  height="300"
                  :row-class-name="() => 'defct-row'"
                >
                  <el-table-column
                    prop="type"
                    label="Type"
                    width="100"
                    align="center"
                  />

                  <el-table-column
                    v-for="n in dynamicColumns"
                    :key="n"
                    :label="`${n}`"
                    align="center"
                  >
                    <template #default="{ row }">
                      <span
                        :style="cellStyle(row[`${n}`], thresholds.defect_trend)"
                      >
                        {{ row[`${n}`] ? row[`${n}`] + "%" : "--" }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="table-row">
            <!-- T defct trend -->
            <el-col :span="24">
              <el-card class="table-card">
                <template #header>
                  <div class="card-header">
                    T Defect ALARM BY TRAY Position ({{
                      thresholds.defect_by_tray_position
                    }}%)
                  </div>
                </template>
                <el-table
                  :data="tableDataTDefctTrend"
                  border
                  height="300"
                  :row-class-name="() => 'defct-row'"
                >
                  <el-table-column
                    prop="type"
                    label="Type"
                    width="100"
                    align="center"
                  />
                  <el-table-column
                    v-for="n in dynamicColumnsTDefctTrend"
                    :key="n"
                    :label="`${n}`"
                    align="center"
                  >
                    <template #default="{ row }">
                      <span
                        :style="
                          cellStyle(
                            row[`${n}`],
                            thresholds.defect_by_tray_position
                          )
                        "
                      >
                        {{ row[`${n}`] ? row[`${n}`] + "%" : "--" }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- T-defect -->
            <el-col :span="24">
              <el-card class="table-card">
                <template #header>
                  <div class="card-header">
                    T Defect ALARM BY Nozzle Hole ({{
                      thresholds.defect_by_hole_number
                    }}%)
                  </div>
                </template>
                <div ref="tableContainer" class="table-container">
                  <el-table
                    :data="tableDataTDefect"
                    border
                    height="300"
                    style="width: 100%"
                    :row-class-name="() => 'defct-row'"
                  >
                    <el-table-column
                      prop="type"
                      label="Type"
                      width="80"
                      align="center"
                    />
                    <el-table-column
                      v-for="n in dynamicColumnsTDefect"
                      :key="n"
                      :label="`${n}`"
                      align="center"
                    >
                      <template #default="{ row }">
                        <span
                          :style="
                            cellStyle(
                              row[`${n}`],
                              thresholds.defect_by_hole_number
                            )
                          "
                        >
                          {{ row[`${n}`] ? row[`${n}`] + "%" : "--" }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-yield"
            :xLine="xAxisDataL"
            :yLine="chartDataPadYield"
            title="BARI IVS Yield"
          />
        </el-col>
      </el-row>

      <!-- Render four charts -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-al-defct"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            title="L-IVS T defct trend"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar-defct"
            :xLine="xAxisDataR"
            :yLine="chartDataDefctR"
            title="R-IVS T defct trend"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
  overflow-x: auto;
}

.chart-row {
  margin-bottom: 20px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
  margin-left: 20px;
}

/* 新增紧凑表格样式 */
:deep(.el-table) {
  --compact-font-size: 12px;
  --compact-padding: 3px;
  --compact-header-bg: #f8f9fa;
}

/* 表头样式 */
:deep(.el-table th) {
  padding: var(--compact-padding) !important;
  background-color: var(--compact-header-bg);
  font-weight: 600;
}

/* 单元格样式 */
:deep(.el-table td) {
  padding: var(--compact-padding) !important;
  line-height: 0.5;
}

/* 卡片标题优化 */
.card-header {
  font-size: 14px;
  font-size: 600;
}

/* 调整卡片间距 */
.table-card {
  margin-bottom: 8px;
}

/* 移除固定高度 */
.el-table {
  height: auto !important;
}

/* 行悬停效果优化 */
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #f5f7fa;
}
</style>
