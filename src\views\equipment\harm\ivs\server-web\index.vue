<script setup lang="ts">
import { onMounted, reactive } from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { transformI18n } from "/@/plugins/i18n";
import { harmAPI } from "/@/api/harm/harm";
import { ocrIcsTxtAPI } from "/@/api/equipment/ocrIcsTxt";

const pageData = reactive({
  groupList: [],
  loading: false,
  dataList: [],
  equipGroupList: [],
  testers: [],
  searchTestInfo: {
    groupId: null,
    testTime: null,
    tester: null,
    position: "1",
    paramType: "ED1"
  },
  searchInfo: {
    groupId: "",
    head_SN: "",
    tray_NO: "",
    tester: "",
    proj_CODE: "",
    // startTime: "",
    // endTime: "",
    test_TIME: "",
    pageSize: 10,
    pageNum: 1,
    total: 0,
    sortColumn: [],
    sortType: "",
    type: 3
  }
});

onMounted(() => {
  // pageData.dateRange = [
  //   dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
  //   dayjs().format("YYYY-MM-DD HH:mm:ss")
  // ];
  pageData.searchTestInfo.testTime = getTodayDate(new Date());
  pageData.searchInfo.test_TIME = pageData.searchTestInfo.testTime;
  getGroupList(true);
});

const getTodayDate = (date: Date) => {
  return dayjs(date).format("YYYY-MM-DD");
};

// const dateRangeChange = (dateRange: [string, string]) => {
//   pageData.dateRange = dateRange;
//   getPage();
// };

const getPage = () => {
  pageData.loading = true;
  pageData.searchInfo.proj_CODE = "";
  harmAPI
    .getPage(pageData.searchInfo)
    .then((res: any) => {
      if (res !== "fail") {
        pageData.dataList = res.records;
        pageData.searchInfo.total = parseInt(res.total);
      }
    })
    .finally(() => (pageData.loading = false));
};

const sizeChange = (pageSize: number) => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const onSearch = () => {
  getPage();
};

// const resetSearch = () => {
//   pageData.searchInfo.headSn = "";
//   pageData.searchInfo.trayNo = "";
//   pageData.searchInfo.tester = "";
//   pageData.searchInfo.projCode = "";
//   // pageData.dateRange = [
//   //   dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
//   //   dayjs().format("YYYY-MM-DD HH:mm:ss")
//   // ];
// };

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const handleSortChange = ({ prop, order }: any) => {
  pageData.searchInfo.sortColumn = prop ? [prop] : [];
  pageData.searchInfo.sortType = order === "ascending" ? "ASC" : "DESC";
  //getPage();
};
const exportData = () => {
  pageData.loading = true;
  const exportParams = Object.fromEntries(
    Object.entries(pageData.searchInfo).map(([key, value]) => [
      key,
      String(value)
    ])
  );
  window.open(
    harmAPI.getDownloadUrl() + new URLSearchParams(exportParams).toString()
  );

  pageData.loading = false;
};
const getGroupList = (loadFlag = false) => {
  pageData.searchInfo.test_TIME = pageData.searchTestInfo.testTime;
  const selectedGroup = pageData.equipGroupList.find(
    group => group.id === pageData.searchTestInfo.groupId
  );
  if (selectedGroup) {
    pageData.searchInfo.proj_CODE = selectedGroup.groupName;
  }
  const param: { testTime: String } = {
    testTime: pageData.searchTestInfo.testTime
  };
  ocrIcsTxtAPI.getGroupSelectLists(param).then(res => {
    if (res !== "fail") {
      pageData.equipGroupList = res;
      if (pageData.equipGroupList.length == 0) {
        pageData.equipGroupList = [];
        pageData.testers = [];
        return;
      }
      pageData.searchTestInfo.groupId = pageData.equipGroupList[0].id;
      pageData.searchInfo.proj_CODE = pageData.equipGroupList[0].groupName;
      getTesterSelectList(loadFlag);
    }
  });
};

let getTesterSelectList = (loadFlag = false) => {
  const param: { groupId: String } = {
    groupId: pageData.searchTestInfo.groupId
  };
  ocrIcsTxtAPI.getTesterSelectList(param).then(res => {
    if (res !== "fail") {
      pageData.testers = Array.isArray(res) ? res : [];
      if (pageData.testers.length > 0) {
        pageData.searchTestInfo.tester = pageData.testers[0].testerName;
        handlerSearch(loadFlag);
      }
    }
  });
};

const handlerSearch = (loadFlag = false) => {
  pageData.searchInfo.tester = pageData.searchTestInfo.tester;
  if (loadFlag) {
    pageData.loading = true;
    getPage();
  }
};
</script>

<template>
  <div>
    <!-- <el-card class="query-card">
      <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
        <el-form-item label="Line ID">
          <el-select
            v-model="pageData.searchInfo.groupId"
            placeholder="Line ID"
            @change="reloadByGroup"
          >
            <el-option
              v-for="item in pageData.groupList"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card> -->
    <el-form :inline="true" :model="pageData.searchTestInfo" />
    <el-card>
      <el-card class="query-card">
        <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
          <el-form-item>
            <el-date-picker
              v-model="pageData.searchTestInfo.testTime"
              type="date"
              placeholder="日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              :clearable="false"
              @change="getGroupList(false)"
            />
          </el-form-item>

          <el-form-item label="Line ID">
            <el-select
              v-model="pageData.searchTestInfo.groupId"
              :placeholder="transformI18n('equipment.groupName')"
              @change="getTesterSelectList(false)"
              :clearable="false"
            >
              <el-option
                v-for="item in pageData.equipGroupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Tester">
            <el-select
              v-model="pageData.searchTestInfo.tester"
              @change="handlerSearch(false)"
              placeholder="equipment"
            >
              <el-option
                v-for="item in pageData.testers"
                :key="item.id"
                :label="item.testerName"
                :value="item.testerName"
              />
            </el-select>
          </el-form-item>
          <!--  <el-form-item label="Test Time">
            <el-date-picker
              v-model="pageData.dateRange"
              type="datetimerange"
              range-separator="To"
              start-placeholder="Start time"
              end-placeholder="End time"
              :disabled-date="disabledDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="dateRangeChange"
            />
          </el-form-item> -->

          <!-- 搜索字段 -->
          <el-form-item label="HEAD_SN">
            <el-input
              v-model="pageData.searchInfo.head_SN"
              placeholder="Input HEAD_SN"
              clearable
            />
          </el-form-item>

          <el-form-item label="TRAY_NO">
            <el-input
              v-model="pageData.searchInfo.tray_NO"
              placeholder="Input TRAY_NO"
              clearable
            />
          </el-form-item>

          <!-- <el-form-item label="PROJ_CODE">
            <el-input
              v-model="pageData.searchInfo.projCode"
              placeholder="Input PROJ_CODE"
              clearable
            />
          </el-form-item> -->

          <!-- 操作按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              :icon="useRenderIcon('search')"
              :loading="pageData.loading"
              @click="onSearch"
            >
              {{ transformI18n("query.search") }}
            </el-button>
            <el-button
              type="success"
              :icon="useRenderIcon('download')"
              :loading="pageData.loading"
              @click="exportData"
            >
              {{ transformI18n("query.export") }}
            </el-button>
            <!-- <el-button
              :icon="useRenderIcon('refresh')"
              :loading="pageData.loading"
              @click="resetSearch"
            >
              {{ transformI18n("query.reset") }} 
            </el-button>-->
          </el-form-item>
        </el-form>
      </el-card>
      <!-- 表格结构修正 -->
      <el-table
        v-loading="pageData.loading"
        :data="pageData.dataList"
        border
        @sort-change="handleSortChange"
      >
        <!-- 固定前九列 -->
        <el-table-column prop="head_SN" fixed="left" label="HEAD_SN" />
        <el-table-column prop="test_TIME" fixed="left" label="TEST_TIME" />
        <el-table-column prop="tray_NO" fixed="left" label="TRAY_NO" />
        <el-table-column prop="tester" fixed="left" label="TESTER" />
        <el-table-column prop="proj_CODE" fixed="left" label="PROJ_CODE" />
        <el-table-column prop="position" label="POSITION" />
        <el-table-column prop="bag_ID" label="BAG_ID" />
        <el-table-column prop="ocr_POSI" label="OCR_POSI" />
        <el-table-column prop="la_OR_LB" label="LA OR LB" />

        <el-table-column
          prop="hga_image_name_with_ocr"
          label="HGA image name with OCR"
        />
        <el-table-column
          prop="hga_height_image_name_with_ocr"
          label="HGA height_ image name with OCR"
        />
        <el-table-column
          prop="hga_height_compensation"
          label="HGA_Height compensation"
        />
        <el-table-column
          prop="autoOffsetOverallX"
          label="AutoOffset OverallX"
        />
        <el-table-column prop="autoOffsetOverallY" label="AutoOffsetOverallY" />
        <el-table-column prop="autoOffsetOverallZ" label="AutoOffsetOverallZ" />
        <el-table-column prop="settingLaserPower" label="SettingLaserPower" />
        <el-table-column
          prop="hga_ivs_image_name_with_ocr"
          label="HGA_IVS image name with OCR"
        />
        <el-table-column prop="ivs_Result" label="IVS Result" />
      </el-table>

      <el-pagination
        v-model:currentPage="pageData.searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
}

.table-title {
  font-weight: bold;
  font-size: 20px;
}

.el-form-item {
  margin-bottom: 12px;
}

.el-input {
  width: 200px;
}

.el-date-editor {
  width: 400px;
}
</style>
