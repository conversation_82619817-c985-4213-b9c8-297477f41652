import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/ics/auto",
  GROUP_LIST = "/group",
  ICS_INFO = "/info",
  RULES = "/rules",
  RULES_TOGGLE = "/rules/toggle",
  RULES_SWITCH = "/rules/switch",
  SPEC = "/spec",
  UPDATE_SPEC = "/spec/update",
  HISTORY = "/history",
  HISTORY_LATEST = "/history/latest",
  STATUS = "/status",
  MANUEL = "/manuel",
  MANUEL_WRITE = "/manuel/write",
  MANUEL_UPDATE_FORMULA = "/manuel/update/formula",
  DATA = "/data",
  PID_DATA = "/pid/data",
  DATA_LATEST = "/data/latest",
  EQUIP_STATUS = "/equip/status"
}

class IcsAutoAdjustAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getGroupList<T>(): Promise<T> {
    return this.get<T>(API.GROUP_LIST);
  }

  getICSInfo<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.ICS_INFO, query);
  }

  fetchRules<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.RULES, query);
  }

  toggleRule<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.RULES_TOGGLE, query);
  }

  fetchSpec<T>(): Promise<T> {
    return this.get<T>(API.SPEC);
  }

  updateSpec<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.UPDATE_SPEC, query);
  }

  fetchHistory<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.HISTORY, query);
  }

  fetchLastestHistory<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.HISTORY_LATEST, query);
  }

  switchRule<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.RULES_SWITCH, query);
  }

  fetchStatus<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.STATUS, query);
  }

  fetchManuelData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.MANUEL, query);
  }

  writePlcData<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.MANUEL_WRITE, query);
  }

  updateFormula<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.MANUEL_UPDATE_FORMULA, query);
  }

  fetchLatestData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.DATA_LATEST, query);
  }

  fetchData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.DATA, query);
  }

  fetchPidData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.PID_DATA, query);
  }

  fetchEquipStatus<T>(): Promise<T> {
    return this.get<T>(API.EQUIP_STATUS);
  }
}

export const icsAutoAdjustAPIs = new IcsAutoAdjustAPI();
