import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/ivs/",
  IVS_POS = "ivsData",
  Pad = "padData",
  GROUP_LIST = "/group",
  ALL = "allData",
  CONFINFO = "/confInfo",
  updateAlarmPercent = "/updateAlarmPercent"
}

class IvsPosAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getIVSData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.IVS_POS, query);
  }

  getPadData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.Pad, query);
  }

  getAllData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.ALL, query);
  }

  getGroupList<T>(): Promise<T> {
    return this.get<T>(API.GROUP_LIST);
  }

  getConfInfo<T>(): Promise<T> {
    return this.get<T>(API.CONFINFO);
  }

  updateAlarmPercent<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.updateAlarmPercent, query);
  }
}

export const ivsPosAPIs = new IvsPosAPI();
