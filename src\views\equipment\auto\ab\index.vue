<script setup lang="ts">
import { onBeforeMount, ref, reactive } from "vue";
import { abAutoAdjustAPI } from "/@/api/local/abAutoAdjust";
import { abAutoAdjustAPIs } from "/@/api/ab/abAutoAdjust";
import localView from "./components/local-client/index.vue";
import remoteView from "./components/server-web/index.vue";
import { useICSAutoStoreHook } from "/@/store/modules/icsAuto";
//import { el } from "element-plus/es/locale";

defineOptions({
  name: "autoIcs"
});

// 0 初始化, 1 Client, 2 Server
const isLocal = ref(0);
//const isData = ref(0);

const componentKey = ref(2);

const icsEquipInfo = reactive({
  ics_l: null,
  ics_r: null
});

const pageData = reactive({
  groupList: [],
  searchInfo: {
    groupId: ""
  }
});

onBeforeMount(() => {
  abAutoAdjustAPI
    .getabInfo()
    .then((res: any) => {
      console.log("res", res);
      if (res !== "fail") {
        if (res.ics_l || res.ics_r) {
          isLocal.value = 1;
          icsEquipInfo.ics_l = res.ics_l;
          icsEquipInfo.ics_r = res.ics_r;
        } else {
          abAutoAdjustAPIs.getGroupList().then((res: any) => {
            pageData.groupList = res;
            pageData.searchInfo.groupId = pageData.groupList[0].groupId;
            getICS(pageData.searchInfo.groupId);
          });
          isLocal.value = 2;
        }
      }
    })
    .catch(_err => {
      abAutoAdjustAPIs.getGroupList().then((res: any) => {
        pageData.groupList = res;
        pageData.searchInfo.groupId = pageData.groupList[0].groupId;
        getICS(pageData.searchInfo.groupId);
      });
      isLocal.value = 2;
    });
});

const getICS = groupId => {
  useICSAutoStoreHook().setGroupId(groupId);
  abAutoAdjustAPIs
    .getICSInfo({ groupId })
    .then((res: any) => {
      if (res !== "fail") {
        icsEquipInfo.ics_l = res.ics_l;
        icsEquipInfo.ics_r = res.ics_r;
        forceRerender();
      }
    })
    .catch(_err => {
      console.log(_err);
    });
  isLocal.value = 2;
};

const reloadByGroup = () => {
  useICSAutoStoreHook().setGroupId(pageData.searchInfo.groupId);
  getICS(pageData.searchInfo.groupId);
};

const forceRerender = () => {
  componentKey.value += 1;
};
</script>

<template>
  <div>
    <!-- <el-card v-if="isData === 2">
      <el-row align="middle">
        <el-col :span="24" align="middle">
          <span style="font-weight: bold; font-size: 20px"> No Data... </span>
        </el-col>
      </el-row>
    </el-card> -->
    <el-card>
      <el-card v-if="isLocal === 2" class="query-card">
        <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
          <el-form-item label="Line ID">
            <el-select
              v-model="pageData.searchInfo.groupId"
              placeholder="Line ID"
              @change="reloadByGroup"
            >
              <el-option
                v-for="item in pageData.groupList"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card>
        <el-row align="middle">
          <el-col :span="24" align="left">
            <span style="font-weight: bold; font-size: 20px">
              AB Auto Adjust
            </span>
          </el-col>
        </el-row>
        <el-divider />
        <localView
          v-if="isLocal === 1 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)"
          :icsEquipInfo="icsEquipInfo"
        />
        <remoteView
          :key="componentKey"
          v-else-if="
            isLocal === 2 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)
          "
          :icsEquipInfo="icsEquipInfo"
        />

        <el-row v-else-if="isLocal === 0" align="middle">
          <el-col :span="24" align="middle">
            <span style="font-weight: bold; font-size: 20px">
              Initializing...
            </span>
          </el-col>
        </el-row>
      </el-card>
    </el-card>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}
</style>
