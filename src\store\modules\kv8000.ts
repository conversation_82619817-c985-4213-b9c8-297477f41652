// src/stores/globalStore.js
import { defineStore } from "pinia";

export const useGlobalStore = defineStore("global", {
  state: () => ({
    max: 40, // 默认最大值
    min: -35 // 默认最小值
  }),
  actions: {
    setMax(value) {
      // 仅当新值大于当前最大值时才更新
      if (value > this.max) {
        this.max = value;
      }
    },
    setMin(value) {
      // 仅当新值小于当前最小值时才更新
      if (value < this.min) {
        this.min = value;
      }
    }
  }
});
