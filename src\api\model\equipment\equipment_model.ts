import { BaseClass, BaseQuery } from "../domain";

export interface EquipmentQuery extends BaseQuery {
  equipmentName?: string;
  jarId?: string;
  equipmentType?: string;
  groupId?: string;
}

export interface Equipment extends BaseClass {
  id?: string;
  equipmentName?: string;
  equipmentCode?: string;
  equipmentType?: string;
  jarId?: string;
  address?: string;
  parentId?: string;
  groupName?: string;
  groupId?: string;
  fileUserDomain?: string;
  fileUserAccount?: string;
  fileUserPws?: string;
}

export interface EquipmentSaveParam extends BaseClass {
  id?: string;
  equipmentName?: string;
  jarId?: string;
  equipmentType?: string;
  equipmentCode?: string;
  address?: string;
  parentId?: string;
  groupId?: string;
  fileUserDomain?: string;
  fileUserAccount?: string;
  fileUserPws?: string;
}

export interface EquipmentJarId {
  id?: string;
  jarId?: string;
}

export interface EquipParameterVo extends BaseClass {
  id?: string;
  parameterName?: string;
  equipmentId?: string;
  idealValue?: Number;
  maxValue?: Number;
  minValue?: Number;
  description?: string;
  isSave: boolean;
}

export interface EquipGroupVo extends BaseClass {
  id?: string;
  groupName?: string;
  description?: string;
  isSave: boolean;
}

export interface TesterGroupVO extends BaseClass {
  id?: string;
  groupName?: string;
  testDate?: string;
}
