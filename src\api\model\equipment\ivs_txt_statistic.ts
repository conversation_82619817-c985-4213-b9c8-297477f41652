import { BaseQuery } from "../domain";

export interface paramList extends BaseQuery {
  start?: string;
  end?: string;
  equipmentId?: number;
  padResult?: string;
}

export interface ivsTxtVO {
  result: string;
  result0: number;
  result1: number;
  result2: number;
  result3: number;
  result4: number;
  result5: number;
  result6: number;
  result7: number;
  result8: number;
  result9: number;
}

export interface ivsTxtDetailVO {
  collectTime: string;
  padResult: string;
  result0: string;
  result1: string;
  result2: string;
  result3: string;
  result4: string;
  result5: string;
  result6: string;
  result7: string;
  result8: string;
  result9: string;
}

export interface seriesDataVO {
  name: string;
  type: string;
  stack: string;
  emphasis: {
    focus: "series";
  };
  data: number[];
}

export interface IvsEquipmentVO {
  id: number;
  equipmentName: string;
  equipmentType: string;
  parentId: number;
  parentName: string;
}
