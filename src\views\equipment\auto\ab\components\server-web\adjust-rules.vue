<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, reactive } from "vue";
import { abAutoAdjustAPIs } from "/@/api/ab/abAutoAdjust";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";
import { emitter } from "/@/utils/mitt";
import { useICSAutoStoreHook } from "/@/store/modules/icsAuto";

const ruleSpec = reactive({
  spec: null,
  control: null
});

const ruleList = ref([]);

/**
 * 自动调整规则类型: 2
 */
const getRules = () => {
  let groupId = useICSAutoStoreHook().getGroupId;
  if (groupId) {
    abAutoAdjustAPIs
      .fetchRules({ groupId, type: 2 })
      .then((res: any) => {
        if (res !== "fail") {
          ruleList.value = res;
        }
      })
      .catch(_err => {
        console.log("error");
      });
  }
};

const toggleRule = rule => {
  abAutoAdjustAPIs
    .toggleRule(rule)
    .then((_res: any) => {
      getRules();
    })
    .catch(_err => {
      console.log("error");
    });
};

const getSpec = () => {
  abAutoAdjustAPIs
    .fetchSpec()
    .then((res: any) => {
      if (res !== "fail") {
        ruleSpec.spec = res.spec;
        ruleSpec.control = res.control;
      }
    })
    .catch(_err => {
      console.log("error");
    });
};

const saveSpec = () => {
  ElMessageBox({
    title: "Please Confirm Update!",
    message:
      "Spec +/- range:" +
      ruleSpec.spec +
      ", Control +/- range:" +
      ruleSpec.control,
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      abAutoAdjustAPIs
        .updateSpec({
          spec: ruleSpec.spec,
          control: ruleSpec.control
        })
        .then((res: any) => {
          if (res !== "fail") {
            emitter.emit("reloadAutoIcsCharts");
          }
          ElMessageBox.alert(res);
        })
        .catch(err => {
          console.log(err);
        });
    })
    .catch(() => {});
};

onMounted(() => {
  getSpec();
  getRules();
});

onBeforeUnmount(() => {
  emitter.all.delete("reloadAutoIcsCharts");
});
</script>

<template>
  <el-descriptions border size="large" :column="1" style="margin-top: 5px">
    <el-descriptions-item label="Rules" label-align="center" align="left">
      <el-row align="middle">
        <span
          >🔺每tray货的参数（A1/B1/A2/B2）mean值范围线为（Target+/-<el-input
            v-model="ruleSpec.spec"
            placeholder="NaN"
            :clearable="false"
            style="width: 50px"
          />) um, 自动调整触发线为（Target+/-<el-input
            v-model="ruleSpec.control"
            placeholder="NaN"
            :clearable="false"
            style="width: 50px"
          />) um <br />
          <el-button type="primary" @click="saveSpec" style="margin-left: 10px"
            >Apply</el-button
          ></span
        >
      </el-row>
      <el-row v-for="rule in ruleList" :key="rule.id">
        <el-col align="left">
          <el-switch
            v-model="rule.enabled"
            @change="toggleRule(rule)"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
              margin-right: 15px;
            "
            active-value="1"
            inactive-value="0"
            active-text="On"
            inactive-text="Off"
            :disabled="true"
          />
          <span>{{ rule.rule }}</span>
        </el-col>
      </el-row>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped></style>
