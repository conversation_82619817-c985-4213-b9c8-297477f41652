<script setup lang="ts">
import { onBeforeMount, ref, watch } from "vue";
import { XJSBB } from "/@/api/xjsbb/xjsbb";
import Chart from "./chart.vue"; // Import chart component
import { ivsPosAPIs } from "/@/api/ivs/ivsPos";

const isCollapsed = ref(false);
// 响应式阈值对象
const thresholds = ref({
  yield: 95,
  defect_trend: 3,
  defect_by_tray_position: 5,
  defect_by_hole_number: 2
});
// Reactive variables for storing different chart data
const chartDataDefctL = ref<any[]>([]);
const chartDataDefctR = ref<any[]>([]);
const chartDataPadYield = ref<any[]>([]);

const xAxisDataL = ref<string[]>([]);
const xAxisDataR = ref<string[]>([]);
const xAxisDataYield = ref<any[]>([]);
const titleName = ref("");
const selectButton = ref("1");

interface SearchInfo {
  groupId: string;
  groupName: string;
}

const props = defineProps<{
  groupList: Array<{ groupId: string; groupName: string }>;
  searchInfo: SearchInfo;
}>();

// Get data function, passing time range as parameters
const fetchData = async (type: string) => {
  XJSBB.IVSData({
    type,
    groupId: props.searchInfo.groupId
  }).then((res: any) => {
    if (res !== "fail") {
      processIVSChartData(res);
    }
  });
};

// Data processing functions
const processIVSChartData = (res: any) => {
  chartDataDefctL.value = getSeriesFromResData(res[titleName.value + "A"]);
  chartDataDefctR.value = getSeriesFromResData(res[titleName.value + "B"]);
  // 只有在不是Hour模式时才处理yield数据
  if (selectButton.value !== "5") {
    chartDataPadYield.value = getYieldSeries(res);
  }
  xAxisDataL.value = getxAxisData(res[titleName.value + "A"]);
  xAxisDataR.value = getxAxisData(res[titleName.value + "B"]);
};

const getxAxisData = (xAxis: any) => {
  return xAxis["xAxis"];
};
const getYieldSeries = (
  data: Record<string, { xAxis: string[]; yield: string[] }>
) => {
  // 获取所有日期的并集
  xAxisDataYield.value = Array.from(
    new Set(Object.values(data).flatMap(item => item.xAxis))
  ).sort((a, b) => {
    // 提取日期部分和类型部分
    const dateA = a.slice(0, -2);
    const typeA = a.slice(-2);
    const dateB = b.slice(0, -2);
    const typeB = b.slice(-2);

    // 先按日期排序
    const dateCompare = dateA.localeCompare(dateB);
    if (dateCompare !== 0) return dateCompare;

    // 同一天时，MS排在ES前
    const typeOrder = { MS: 0, ES: 1 };
    return (typeOrder[typeA] ?? 2) - (typeOrder[typeB] ?? 2);
  });
  // 构建系列数据
  const series = Object.keys(data).map(key => {
    //过滤最后一个字母是A或B的
    if (key.endsWith("A") || key.endsWith("B")) {
      return null;
    }
    const seriesData = xAxisDataYield.value.map(date => {
      const index = data[key].xAxis.indexOf(date);
      return index !== -1 ? parseFloat(data[key].yield[index]) : null;
    });
    return {
      name: key,
      type: "line",
      stack: key,
      yAxisIndex: 0,
      emphasis: {
        focus: "series"
      },
      markPoint: null,
      markLine: null,
      data: seriesData
    };
  });
  // 过滤掉 null 值的系列
  const filteredSeries = series.filter(item => item !== null);
  console.log(filteredSeries, "data");
  return filteredSeries;
};
// Helper function for chart data formatting
const getSeriesFromResData = (series: any) => {
  const seriesData: any[] = [];
  if (series) {
    Object.keys(series).forEach(key => {
      if (key === "xAxis" || key === "yield") {
        return;
      }
      seriesData.push({
        name: key.toUpperCase(),
        type: "line",
        stack: key,
        yAxisIndex: 0,
        emphasis: {
          focus: "series"
        },
        markPoint: null,
        markLine: null,
        data: series[key] || []
      });
    });
  }
  return seriesData;
};

const getType = (type: string) => {
  selectButton.value = type;
  fetchData(type);
};

onBeforeMount(() => {
  setTimeout(() => {
    loadSettings();
    titleName.value = props.searchInfo.groupName;
    getType(selectButton.value);
  }, 50);
});

//监听groupId变化
watch(
  () => props.searchInfo.groupId,
  () => {
    setTimeout(() => {
      const groupNames = props.groupList
        .find(g => g.groupId === props.searchInfo.groupId)
        .groupName.split("_");
      //props.searchInfo.groupName = groupNames[groupNames.length - 1];
      titleName.value = groupNames[groupNames.length - 1];
      console.log(
        titleName.value,
        "titleName.value",
        props.searchInfo.groupName
      );
      fetchData(selectButton.value);
    }, 50);
  }
);

// 新增：保存设置到数据库
const updateThreshold = async (type: string) => {
  const value = thresholds.value[type];
  // 保存到数据库
  ivsPosAPIs.updateAlarmPercent({ name: type, value }).then((res: any) => {
    if (res !== "fail") {
      loadSettings();
    }
  });
};
// 新增：从数据库加载设置
const loadSettings = async () => {
  ivsPosAPIs.getConfInfo().then((res: any) => {
    if (res !== "fail") {
      res.forEach(item => {
        console.log(Number(item.confValue));
        thresholds.value[item.confName] = Number(item.confValue);
      });
    }
  });
};
</script>

<template>
  <div>
    <div>
      <!-- Buttons for Shift and Time Range Selection -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="120px" class="filter-form">
            <!-- Time Range Buttons -->
            <el-form-item>
              <el-tooltip
                content="Only show in T defect trend"
                placement="top"
                effect="light"
              >
                <el-button
                  class="custom-button"
                  :class="{ active: selectButton === '5' }"
                  @click="getType('5')"
                >
                  Hour
                </el-button>
              </el-tooltip>
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '1' }"
                @click="getType('1')"
                >Shift</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '2' }"
                @click="getType('2')"
                >Daily</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '3' }"
                @click="getType('3')"
                >SAE Weekly</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === '4' }"
                @click="getType('4')"
                >Monthly</el-button
              >
            </el-form-item>
            <!-- Date Picker -->
          </el-form>
        </el-col>
      </el-row>
      <div>
        <el-row class="collapse-btn-row">
          <el-col :span="24" style="text-align: left; margin-bottom: 10px">
            <el-button
              @click="isCollapsed = !isCollapsed"
              type="primary"
              size="small"
            >
              {{ isCollapsed ? "SHOW TABLE" : "HIDE TABLE" }}
            </el-button>
          </el-col>
        </el-row>

        <div v-show="!isCollapsed">
          <el-row :gutter="20" class="settings-container">
            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="BARI IVS Yield">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.yield"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('yield')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="IVS Defect Threshold">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_trend"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('defect_trend')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="T Defect By Tray Position">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_by_tray_position"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('defect_by_tray_position')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>

            <el-col :xs="24" :md="12" class="setting-item">
              <div class="setting-card">
                <el-form-item label="T-Defect By Nozzle Hole">
                  <div class="input-row">
                    <el-input
                      v-model.number="thresholds.defect_by_hole_number"
                      placeholder="0-100"
                      :min="0"
                      :max="100"
                      type="number"
                    >
                      <template #append>%</template>
                    </el-input>
                    <el-button
                      type="primary"
                      class="action-btn"
                      @click="updateThreshold('defect_by_hole_number')"
                      v-auth="['icsYield:update']"
                    >
                      update
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-chart-yield"
            :xLine="xAxisDataYield"
            :yLine="chartDataPadYield"
            :alarmLine="thresholds.yield"
            title="BARI IVS Yield"
          />
        </el-col>
      </el-row>

      <!-- Render four charts -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-l-defct"
            :xLine="xAxisDataL"
            :yLine="chartDataDefctL"
            :alarmLine="thresholds.defect_trend"
            :title="titleName + ' L-IVS T defct trend'"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar-defct"
            :xLine="xAxisDataR"
            :yLine="chartDataDefctR"
            :alarmLine="thresholds.defect_trend"
            :title="titleName + ' R-IVS T defct trend'"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
@media (max-width: 768px) {
  .settings-row {
    padding: 12px;
  }

  .el-col {
    margin-bottom: 8px;
  }

  .button-col {
    text-align: center;
    margin-top: 16px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .setting-item {
    flex: 1 1 100%;
  }

  .input-row {
    gap: 8px;
  }

  .action-btn {
    width: 70px;
    padding: 0 10px;
  }
}

.settings-row {
  margin-bottom: 20px;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.compact-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    color: #606266;
    padding-bottom: 4px;
  }
}

.compact-input {
  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}

.button-col {
  text-align: left;
  margin-top: 12px;
}

.submit-btn {
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.chart-row {
  margin-bottom: 20px;
  margin-top: 16px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
  margin-left: 20px;
}

.settings-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.setting-item {
  flex: 1 1 calc(50% - 20px);
  min-width: 300px;
}

.setting-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.input-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.el-input {
  flex: 1;
  min-width: 120px;
}

.action-btn {
  flex-shrink: 0;
  width: 80px;
  height: 32px;
  padding: 0 12px;
}

.el-form-item__label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  height: 32px;
  padding: 0 15px;
} /* 响应式调整 */

.question-icon {
  font-size: 16px;
  color: #909399;
  margin-left: 5px;
  cursor: help;
}

.question-icon:hover {
  color: #409eff;
}
</style>
