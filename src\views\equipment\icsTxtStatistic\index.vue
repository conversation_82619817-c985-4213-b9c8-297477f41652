<script setup lang="ts">
import { icsTxtStatisticAPI } from "/@/api/equipment/icsTxtStatistic";
import { onMounted, reactive } from "vue";
import {
  paramList,
  icsTxtVO,
  IcsEquipmentVO
} from "/@/api/model/equipment/ics_txt_statistic";
import Line from "./Line.vue";

defineOptions({
  name: "icsTxtStatistic"
});

const pageData = reactive<{
  componentKey: number;
  line: boolean;
  loading: boolean;
  selection: any[];
  testList: icsTxtVO[];
  searchTestInfo: paramList;
  IcsEquipmentList: IcsEquipmentVO[];
  posList: Array<{ name: string; value: string }>;
  dates: string;
}>({
  componentKey: 1,
  line: true,
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    collectDate: "",
    equipmentId: null,
    pos: ""
  },
  IcsEquipmentList: [],
  posList: [
    { name: "平均值", value: "" },
    { name: "P1", value: "1" },
    { name: "P2", value: "2" },
    { name: "P3", value: "3" },
    { name: "P4", value: "4" },
    { name: "P5", value: "5" },
    { name: "P6", value: "6" },
    { name: "P7", value: "7" },
    { name: "P8", value: "8" },
    { name: "P9", value: "9" },
    { name: "P10", value: "10" }
  ],
  dates: ""
});

const shortcuts = [
  {
    text: "Last week",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "Last month",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "Last 3 months",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

const getPage = () => {
  if (pageData.searchTestInfo.equipmentId == null) {
    return;
  }
  pageData.loading = true;
  pageData.line = false;
  icsTxtStatisticAPI
    .list(pageData.searchTestInfo)
    .then((res: icsTxtVO[] | string) => {
      if (res !== "fail") {
        pageData.testList = res as icsTxtVO[];
      }
    })
    .finally(() => {
      pageData.loading = false;
      pageData.line = true;
      pageData.componentKey += 1;
    });
};

let getEquipmentSelectList = () => {
  icsTxtStatisticAPI
    .getEquipmentSelectList()
    .then((res: IcsEquipmentVO[] | string) => {
      if (res !== "fail") {
        pageData.IcsEquipmentList = res as IcsEquipmentVO[];
        pageData.searchTestInfo.equipmentId = pageData.IcsEquipmentList[0].id;
        getPage();
      }
    });
};

const getTodayDate = (date: Date) => {
  let month: string | number = date.getMonth() + 1;
  let strDate: string | number = date.getDate();

  if (month <= 9) {
    month = "0" + month;
  }

  if (strDate <= 9) {
    strDate = "0" + strDate;
  }

  return date.getFullYear() + "-" + month + "-" + strDate;
};

onMounted(() => {
  pageData.searchTestInfo.collectDate = getTodayDate(new Date());
  getEquipmentSelectList();
});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const handlerSearch = () => {
  console.log(pageData.dates);
  if (pageData.dates != null && pageData.dates != "") {
    pageData.searchTestInfo.collectDate = pageData.dates[0];
  }
  getPage();
};

const handelSelectionChange = val => {
  pageData.selection = val;
};
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.equipmentId"
          placeholder="设备"
        >
          <el-option
            v-for="item in pageData.IcsEquipmentList"
            :key="item.id"
            :label="item.equipmentName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="pageData.searchTestInfo.collectDate"
          type="date"
          placeholder="日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          :shortcuts="shortcuts"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item>
        <el-select v-model="pageData.searchTestInfo.pos" placeholder="位置">
          <el-option
            v-for="item in pageData.posList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="handlerSearch">Query</el-button>
      </el-form-item>
    </el-form>

    <el-card>
      <template #default>
        <Line :txt="pageData.searchTestInfo" :key="pageData.componentKey" />
      </template>
    </el-card>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        @selection-change="handelSelectionChange"
      >
        <el-table-column
          sortable
          resizable
          :show-overflow-tooltip="true"
          type="selection"
        />
        <el-table-column
          prop="result"
          label="结果"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result1"
          label="P1"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result2"
          label="P2"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result3"
          label="P3"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result4"
          label="P4"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result5"
          label="P5"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result6"
          label="P6"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result7"
          label="P7"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result8"
          label="P8"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result9"
          label="P9"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="result10"
          label="P10"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
      </el-table>
    </el-card>
  </div>
</template>

<style scoped></style>
