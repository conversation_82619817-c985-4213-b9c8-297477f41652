<script setup lang="ts">
import { onBeforeMount, onMounted, onUnmounted, ref, reactive } from "vue";
import clientPanel from "./client-panel.vue";
import icsPanel from "./ics-panel.vue";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const appMainDom = ref();
const pageSize = reactive({
  width: 0,
  height: 0
});

onBeforeMount(() => {
  appMainDom.value = document.getElementById("appMain");
  pageSize.width = appMainDom.value.clientWidth;
  pageSize.height = appMainDom.value.clientHeight;
  window.onresize = () => {
    return (() => {
      pageSize.width = appMainDom.value.clientWidth;
      pageSize.height = appMainDom.value.clientHeight;
    })();
  };
});

onMounted(() => {
  console.log(props.icsEquipInfo.ics_l);
  console.log(props.icsEquipInfo.ics_r);
});

onUnmounted(() => {
  toggleAlarm(false);
});

const toggleAlarm = (signal: boolean) => {
  if (signal) {
    appMainDom.value.classList.add("warningbg");
  } else {
    appMainDom.value.classList.remove("warningbg");
  }
};

// TODO: 1.开始接入数据, 定义接口
// 2.icsPanel定义ICS-L或ICS-R, 传入对应的equipment信息
// 3.icsChart定义ED1或ED2, 日期选中最多不能超过7日, 且默认为当日. GLUE/EX/EY数据从KV8000获取, ED数据从Log获取
// 4.HDS Client启动一个定时器, 执行频率为1秒. 根据规则分析数据并作出调整, 且调整的Log建表入库. 在History中展示, History默认展示当日的操作
// 5.实时数据分析需要缓存
// 6.规则部分, 可能对个别变量的描述设为输入框, 可由工程师修改
// 7.icsPanel部分可能需要扩展某些变量的输入框, 可由工程师修改
// 8.保存工程师修改的记录
// 9.触发报警时弹出对话框并显示报警信息让工程师确认, 确认后消除报警
</script>

<template>
  <div>
    <clientPanel :icsEquipInfo="props.icsEquipInfo" />
    <el-divider v-if="props.icsEquipInfo.ics_l" />
    <icsPanel
      v-if="props.icsEquipInfo.ics_l"
      :icsEquipInfo="props.icsEquipInfo.ics_l"
      :pageWidth="pageSize.width"
      :pageHeight="pageSize.height"
    />
    <el-divider v-if="props.icsEquipInfo.ics_r" />
    <icsPanel
      v-if="props.icsEquipInfo.ics_r"
      :icsEquipInfo="props.icsEquipInfo.ics_r"
      :pageWidth="pageSize.width"
      :pageHeight="pageSize.height"
    />
  </div>
</template>

<style scoped></style>
