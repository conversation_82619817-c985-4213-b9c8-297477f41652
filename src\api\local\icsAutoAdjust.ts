import { http } from "/@/utils/http";

class IcsAutoAdjustAPI {
  getICSInfo(): any {
    return http.request("get", "http://localhost:8080/client/ics/info", null);
  }

  fetchStatus(): any {
    return http.request("get", "http://localhost:8080/client/ics/status", null);
  }

  fetchManuelData(params): any {
    return http.request("get", "http://localhost:8080/client/ics/manuel", {
      params
    });
  }

  writePlcData(data): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ics/manuel/write",
      { data: data }
    );
  }

  updateFormula(data): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ics/manuel/update/formula",
      { data: data }
    );
  }

  fetchData(params): any {
    return http.request("get", "http://localhost:8080/client/ics/data", {
      params
    });
  }

  fetchLatestData(params): any {
    return http.request("get", "http://localhost:8080/client/ics/data/latest", {
      params
    });
  }

  fetchRules(params): any {
    return http.request("get", "http://localhost:8080/client/ics/rules", {
      params
    });
  }

  toggleRule(rule): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ics/rules/toggle",
      { data: rule }
    );
  }

  switchRule(rule): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ics/rules/switch",
      { data: rule }
    );
  }

  fetchSpec(): any {
    return http.request("get", "http://localhost:8080/client/ics/spec", null);
  }

  updateSpec(data): any {
    return http.request(
      "post",
      "http://localhost:8080/client/ics/spec/update",
      { data: data }
    );
  }

  fetchHistory(params): any {
    return http.request("get", "http://localhost:8080/client/ics/history", {
      params
    });
  }

  fetchLastestHistory(params): any {
    return http.request(
      "get",
      "http://localhost:8080/client/ics/history/latest",
      {
        params
      }
    );
  }

  fetchPidData(params): any {
    return http.request("get", "http://localhost:8080/client/ics/pid/data", {
      params
    });
  }
}

export const icsAutoAdjustAPI = new IcsAutoAdjustAPI();
