<script setup lang="ts">
import { onMounted, reactive, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { icsAutoAdjustAPI } from "/@/api/local/icsAutoAdjust";
import dayjs from "dayjs";
import { cloneDeep } from "lodash-unified";
import SockJS from "sockjs-client/dist/sockjs.min.js";
import Stomp from "stompjs";

let socket = null;
let stompClient = null;
let url = "";
const disconnect = () => {
  if (socket != null) {
    stompClient.send("/app/pid/unsubscribeTopicInfo", {}, url);
    socket.close();
    socket = null;
  }
};
const props = defineProps({
  equipId: {
    require: true,
    type: String
  },
  chartId: {
    require: true,
    type: String
  },
  chartParam: {
    require: true,
    type: String
  },
  cWidth: {
    require: true,
    type: String
  },
  cHeight: {
    require: true,
    type: String
  },
  selectedDates: {
    require: true,
    type: Array,
    default: null
  }
});

let myChart = null;

////let timer = null;

let hasData = false;

let seriesIdx = 0;

const chartDatas = reactive({
  xAxisData: [],
  series0Data: [],
  series1Data: [],
  series2Data: [],
  scatterData: [],
  artificiallyAdjustData: [],
  specs: null,
  series0: {
    yMax: 0,
    yMin: 0,
    muc: 0,
    mlc: 0,
    mus: 0,
    mls: 0,
    mui: undefined,
    mli: undefined
  },
  seriesIdxs: {
    series0: 0,
    series1: 1,
    seriesScatter: 2,
    series2: 3
  }
});

const optionTemplate = {
  graphic: {
    type: "text",
    left: "center",
    top: "middle",
    silent: true,
    invisible: false,
    style: {
      fill: "#9d9d9d",
      fontWeight: "bold",
      text: "No Data",
      fontFamily: "Microsoft YaHei",
      fontSize: 25
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  toolbox: {
    show: true,
    feature: {
      dataZoom: {
        yAxisIndex: "none"
      },
      dataView: { readOnly: false },
      restore: {}
    }
  },
  dataZoom: [
    //滚动条
    {
      type: "slider",
      realtime: true,
      xAxisIndex: [0],
      bottom: "10",
      height: 10,
      borderColor: "rgba(0,0,0,0)",
      textStyle: {
        color: "#05D5FF"
      }
    }
  ],
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: [],
    axisLabel: {
      interval: "auto",
      rotate: 45,
      formatter: function (value, _index) {
        let label = value.substring(
          value.indexOf("-") + 1,
          value.lastIndexOf(".")
        );
        return label.replace("-", "/");
      }
    }
  },
  yAxis: [],
  series: []
};

let option = cloneDeep(optionTemplate);

const initChart = () => {
  myChart = echarts.init(document.getElementById(props.chartId));
};

const initSeries0 = idx => {
  if (chartDatas.xAxisData === undefined) {
    chartDatas.xAxisData = [];
  }
  if (chartDatas.series0Data === undefined) {
    chartDatas.series0Data = [];
  }
  if (chartDatas.series0Data.length > 0) {
    hasData = true;
  }
  calculateSeriesMaxMin();
  option["visualMap"] = [];
  // x轴
  option.xAxis.data = chartDatas.xAxisData;
  // y轴
  option.yAxis.push({
    position: "left",
    type: "value",
    name: props.chartParam
  });

  let series0 = {
    name: props.chartParam,
    symbol: "emptyCircle",
    yAxisIndex: 0,
    zLevel: idx,
    z: idx,
    data: [],
    type: "line",
    markLine: {
      symbol: "none",
      silent: true,
      data: []
    }
  };

  // 只有Glue Time没有规格
  if (props.chartParam.indexOf("GT") < 0) {
    let visualMap = {
      show: false,
      seriesIndex: 0,
      pieces: []
    };
    option.yAxis[0]["min"] = chartDatas.series0.yMin;
    option.yAxis[0]["max"] = chartDatas.series0.yMax;

    // Spec以外颜色
    visualMap.pieces.push({
      gt: chartDatas.series0.mus,
      color: "red"
    });

    // Control以外颜色
    visualMap.pieces.push({
      lte: chartDatas.series0.mus,
      gt: chartDatas.series0.muc,
      color: "orange"
    });

    if (chartDatas.series0.mui !== undefined) {
      // Control以外颜色
      visualMap.pieces.push({
        lte: chartDatas.series0.muc,
        gt: chartDatas.series0.mui,
        color: "rgb(0, 136, 255)"
      });
      // Control以内颜色
      visualMap.pieces.push({
        gte: chartDatas.series0.mli,
        lte: chartDatas.series0.mui,
        color: "green"
      });

      visualMap.pieces.push({
        gte: chartDatas.series0.mlc,
        lt: chartDatas.series0.mli,
        color: "rgb(0, 136, 255)"
      });
    } else {
      visualMap.pieces.push({
        gte: chartDatas.series0.mlc,
        lte: chartDatas.series0.muc,
        color: "green"
      });
    }

    // Control以外颜色
    visualMap.pieces.push({
      gte: chartDatas.series0.mls,
      lt: chartDatas.series0.mlc,
      color: "orange"
    });

    // Spec以外颜色
    visualMap.pieces.push({
      lt: chartDatas.series0.mls,
      color: "red"
    });

    // Spec线
    series0.markLine.data.push({
      yAxis: chartDatas.series0.mus,
      label: {
        formatter: "USL=" + chartDatas.series0.mus,
        fontSize: 12,
        fontWeight: "bolder",
        color: "red",
        position: "middle"
      },
      lineStyle: {
        color: "red",
        type: "dashed"
      }
    });

    series0.markLine.data.push({
      yAxis: chartDatas.series0.mls,
      label: {
        formatter: "LSL=" + chartDatas.series0.mls,
        fontSize: 12,
        fontWeight: "bolder",
        color: "red",
        position: "middle"
      },
      lineStyle: {
        color: "red",
        type: "dashed"
      }
    });

    series0.markLine.data.push({
      yAxis: chartDatas.series0.muc,
      label: {
        formatter: "UCL=" + chartDatas.series0.muc,
        fontSize: 12,
        fontWeight: "bolder",
        color: "orange",
        position: "middle"
      },
      lineStyle: {
        color: "orange",
        type: "dashed"
      }
    });

    series0.markLine.data.push({
      yAxis: chartDatas.series0.mlc,
      label: {
        formatter: "LCL=" + chartDatas.series0.mlc,
        fontSize: 12,
        fontWeight: "bolder",
        color: "orange",
        position: "middle"
      },
      lineStyle: {
        color: "orange",
        type: "dashed"
      }
    });

    if (chartDatas.series0.mui !== undefined) {
      series0.markLine.data.push({
        yAxis: chartDatas.series0.mui,
        label: {
          formatter: "UIL=" + chartDatas.series0.mui,
          fontSize: 12,
          fontWeight: "bolder",
          color: "blue",
          position: "middle"
        },
        lineStyle: {
          color: "rgb(0, 136, 255)",
          type: "dashed"
        }
      });

      series0.markLine.data.push({
        yAxis: chartDatas.series0.mli,
        label: {
          formatter: "LIL=" + chartDatas.series0.mli,
          fontSize: 12,
          fontWeight: "bolder",
          color: "blue",
          position: "middle"
        },
        lineStyle: {
          color: "rgb(0, 136, 255)",
          type: "dashed"
        }
      });
    }

    option["visualMap"].push(visualMap);
  }
  // 数据
  series0.data = chartDatas.series0Data;
  // 数据名
  series0.name = props.chartParam;

  option.series.push(series0);
};

const calculateSeriesMaxMin = () => {
  let sortList = chartDatas.series0Data.slice();
  sortList.sort((a, b) => {
    let av = parseInt(a);
    let bv = parseInt(b);
    return av < bv ? -1 : av > bv ? 1 : 0;
  });
  chartDatas.series0.yMin = sortList[0];
  chartDatas.series0.yMax =
    sortList[sortList.length - 1] === undefined
      ? 0
      : sortList[sortList.length - 1];
  chartDatas.series0.yMax =
    chartDatas.series0.yMax > chartDatas.series0.mus
      ? chartDatas.series0.yMax
      : chartDatas.series0.mus;
  chartDatas.series0.yMin =
    chartDatas.series0.yMin < chartDatas.series0.mls
      ? chartDatas.series0.yMin
      : chartDatas.series0.mls;
};

const initSeries1 = idx => {
  if (chartDatas.series1Data === undefined) {
    chartDatas.series1Data = [];
  }
  if (chartDatas.series1Data.length > 0) {
    hasData = true;
  }
  option.yAxis.push({
    position: "right",
    type: "value",
    name: props.chartParam.replace("ED", "GT") + " PLC"
  });
  let series1 = {
    symbol: "emptyCircle",
    yAxisIndex: 1,
    zLevel: idx,
    z: idx,
    data: [],
    type: "line",
    itemStyle: {
      color: function (params) {
        // 根据 artificiallyAdjustData 设置颜色
        if (
          chartDatas.artificiallyAdjustData &&
          chartDatas.artificiallyAdjustData[params.dataIndex] === "1"
        ) {
          return "green";
        }
        return "blue";
      }
    },
    lineStyle: {
      color: "blue" // 线条保持蓝色
    }
  };
  // 数据
  series1.data = chartDatas.series1Data;
  // 数据名
  series1["name"] = props.chartParam.replace("ED", "GT") + " PLC";

  option.series.push(series1);
  chartDatas.seriesIdxs.series1 = option.series.length - 1;
};

const initSeries2 = idx => {
  if (chartDatas.series2Data === undefined) {
    chartDatas.series2Data = [];
  }
  if (chartDatas.series2Data.length > 0) {
    hasData = true;
  }
  // x轴
  option.yAxis.push({
    position: "left",
    type: "value",
    name: props.chartParam
  });
  let series2 = {
    symbol: "emptyCircle",
    yAxisIndex: 0,
    zLevel: idx,
    z: idx,
    data: [],
    type: "line",
    itemStyle: {
      color: "rgb(128,128,128)"
    }
  };
  // 添加 visualMap 处理超出范围的颜色
  if (chartDatas.series0.mui !== undefined) {
    let visualMapSeries2 = {
      show: false,
      seriesIndex: option.series.length,
      pieces: []
    };

    // Control以内颜色
    visualMapSeries2.pieces.push({
      gte: chartDatas.series0.mli,
      lte: chartDatas.series0.mui,
      color: "rgb(128,128,128)"
    });

    visualMapSeries2.pieces.push({
      gt: chartDatas.series0.mui,
      color: "rgb(48,48,48)"
    });

    visualMapSeries2.pieces.push({
      lt: chartDatas.series0.mli,
      color: "rgb(48,48,48)"
    });

    // 添加 visualMap 配置到 option
    option["visualMap"].push(visualMapSeries2);
  }

  // 数据
  series2.data = chartDatas.series2Data;
  // 数据名
  series2["name"] = "Predict";

  option.series.push(series2);
  chartDatas.seriesIdxs.series2 = option.series.length - 1;
};

const initSeriesScatter = idx => {
  if (chartDatas.scatterData === undefined) {
    chartDatas.scatterData = [];
  }
  if (chartDatas.scatterData.length > 0) {
    hasData = false;
  }
  let seriesScatter = {
    yAxisIndex: 1,
    zLevel: idx,
    z: idx,
    symbolSize: 10,
    data: [],
    type: "scatter",
    itemStyle: {
      color: "purple" //改变折线点的颜色
    }
  };
  seriesScatter.data = chartDatas.scatterData;
  seriesScatter["name"] = "Adjust";

  option.series.push(seriesScatter);
  chartDatas.seriesIdxs.seriesScatter = option.series.length - 1;
};

const fetchLatestData = () => {
  console.log("Connected equip: " + props.equipId + props.chartParam);
  if (socket == null) {
    socket = new SockJS("http://localhost:8080/ws");
    stompClient = Stomp.over(socket);
  }
  stompClient.connect({}, () => {
    url = "/topic/pid/" + props.equipId + "/" + props.chartParam;
    stompClient.debug = () => {};
    stompClient.send("/app/pid/subscribeTopicInfo", {}, url);
    stompClient.subscribe(url, res => {
      res = JSON.parse(res.body).data;
      if (res.xAxisData !== undefined) {
        chartDatas.xAxisData.push(...res.xAxisData);
        if (chartDatas.xAxisData.length > 0) {
          hasData = true;
        }
      }
      if (res.series0Data !== undefined) {
        chartDatas.series0Data.push(...res.series0Data);
      }
      // append series0
      option.xAxis.data = chartDatas.xAxisData;
      calculateSeriesMaxMin();
      if (props.chartParam.indexOf("GT") < 0) {
        option.yAxis[0].min = chartDatas.series0.yMin;
        option.yAxis[0].max = chartDatas.series0.yMax;
      }
      option.series[0].data = chartDatas.series0Data;
      // init series1
      if (res.series1Data !== undefined) {
        if ((seriesIdx & 2) === 0) {
          chartDatas.series1Data = res.series1Data;
          // 处理 artificiallyAdjustData
          if (res.artificiallyAdjustData !== undefined) {
            chartDatas.artificiallyAdjustData = res.artificiallyAdjustData;
          }
          initSeries1((seriesIdx |= 2));
        } else {
          chartDatas.series1Data.push(...res.series1Data);
          // 处理 artificiallyAdjustData
          if (res.artificiallyAdjustData !== undefined) {
            chartDatas.artificiallyAdjustData.push(
              ...res.artificiallyAdjustData
            );
          }
          option.series[chartDatas.seriesIdxs.series1].data =
            chartDatas.series1Data;
          // 重新设置 series1 的样式以应用新的颜色
          option.series[chartDatas.seriesIdxs.series1].itemStyle = {
            color: function (params) {
              if (
                chartDatas.artificiallyAdjustData &&
                chartDatas.artificiallyAdjustData[params.dataIndex] === "1"
              ) {
                return "green";
              }
              return "blue";
            }
          };
          option.series[chartDatas.seriesIdxs.series1].lineStyle = {
            color: "blue" // 保持线条为蓝色
          };
        }
      }
      // pid调节点不展示
      if (res.scatterData !== undefined) {
        if ((seriesIdx & 4) === 0) {
          chartDatas.scatterData = res.scatterData;
          initSeriesScatter((seriesIdx |= 4));
        } else {
          // chartDatas.scatterData.push.apply(
          //   chartDatas.scatterData,
          //   res.scatterData
          // );
          chartDatas.scatterData = res.scatterData;
          option.series[chartDatas.seriesIdxs.seriesScatter].data =
            chartDatas.scatterData;
        }
      }
      // init series2Data 预测线
      if (res.series2Data !== undefined) {
        if ((seriesIdx & 8) === 0) {
          chartDatas.series2Data = res.series2Data;
          initSeries2((seriesIdx |= 8));
        } else {
          chartDatas.series2Data.push(...res.series2Data);
          option.series[chartDatas.seriesIdxs.series2].data =
            chartDatas.series2Data;
        }
      }

      option.graphic.invisible = hasData;
      option && myChart.setOption(option);
    });
  });
};

const initData = selectedDates => {
  if (selectedDates.length > 1) {
    selectedDates = selectedDates.join(",");
  }
  myChart.showLoading();
  icsAutoAdjustAPI
    .fetchPidData({
      equipId: props.equipId,
      parameter: props.chartParam,
      selected: selectedDates
    })
    .then((res: any) => {
      if (res !== "fail") {
        option = cloneDeep(optionTemplate);
        seriesIdx = 0;
        hasData = false;
        chartDatas.xAxisData = res.xAxisData;
        chartDatas.series0Data = res.series0Data;
        chartDatas.series1Data = res.series1Data;
        chartDatas.series2Data = res.series2Data;
        chartDatas.scatterData = res.scatterData;
        chartDatas.artificiallyAdjustData = res.artificiallyAdjustData || [];
        if (res.specs !== undefined) {
          chartDatas.series0.muc = parseFloat(res.specs.muc);
          chartDatas.series0.mlc = parseFloat(res.specs.mlc);
          chartDatas.series0.mus = parseFloat(res.specs.mus);
          chartDatas.series0.mls = parseFloat(res.specs.mls);
          if (res.specs.mui !== undefined) {
            chartDatas.series0.mui = parseFloat(res.specs.mui);
          }
          if (res.specs.mli !== undefined) {
            chartDatas.series0.mli = parseFloat(res.specs.mli);
          }
        }
        // init series0
        if ((seriesIdx & 1) === 0) {
          initSeries0((seriesIdx |= 1));
        }
        // init series1
        if (res.series1Data !== undefined && (seriesIdx & 2) === 0) {
          initSeries1((seriesIdx |= 2));
        }
        // init seriesScatter
        if (res.scatterData !== undefined && (seriesIdx & 4) === 0) {
          initSeriesScatter((seriesIdx |= 4));
        }
        if (res.series2Data !== undefined && (seriesIdx & 8) === 0) {
          initSeries2((seriesIdx |= 8));
        }

        option.graphic.invisible = hasData;
        option && myChart.setOption(option);

        let now = dayjs().format("YYYY-MM-DD HH:mm:ss.SSS");
        let nowYMD = now.substring(0, now.lastIndexOf(" "));
        // 当选择日期含有今日时,定时请求新数据
        if (props.selectedDates.includes(nowYMD)) {
          fetchLatestData();
        } else {
          disconnect();
        }
      }
    })
    .catch(err => {
      console.log(err);
    })
    .finally(() => {
      myChart.hideLoading();
    });
};

onMounted(() => {
  // 延迟加载, 否则长宽无法获取
  setTimeout(() => {
    initChart();
    initData(props.selectedDates);
  }, 50);
  window.addEventListener("beforeunload", handleBeforeUnload);
});

onBeforeUnmount(() => {
  disconnect();
  //clearTimer();
});
const handleBeforeUnload = () => {
  //页面直接关闭
  disconnect();
  window.removeEventListener("beforeunload", handleBeforeUnload);
};

const reloadData = selectedDates => {
  initData(selectedDates);
};

const clickOnChart = () => {
  // Maybe add right click image view in future
};

defineExpose({
  reloadData
});
</script>

<template>
  <div
    @click.stop="clickOnChart"
    :id="chartId"
    :style="{
      width: props.cWidth,
      height: props.cHeight,
      margin: 0,
      padding: 0
    }"
  />
</template>

<style scoped></style>
