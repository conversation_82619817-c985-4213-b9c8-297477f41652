<script setup lang="ts">
import { onMounted, reactive } from "vue";
import { acrAPIs } from "/@/api/acr/acrAPI";
import { useAcrStoreHook } from "/@/store/modules/acr";

defineOptions({
  name: "AcrOrder"
});

const pageData = reactive({
  loading: false,
  selection: [],
  dataList: [],
  searchInfo: {
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  }
});

const getPage = () => {
  pageData.loading = true;
  pageData.dataList = [];
  acrAPIs
    .fetchOrderList(pageData.searchInfo)
    .then((res: any) => {
      if (res !== "fail") {
        res.records.map((item, _index) => {
          pageData.dataList.push(
            Object.assign(item, { alterRoleVisible: false })
          );
        });
        pageData.searchInfo.total = Number(res.total);
      }
    })
    .finally(() => (pageData.loading = false));
};

onMounted(() => {
  getPage();
});

const rowState = ({ row }) => {
  if (row.isDeleted === "1") {
    return {
      backgroundColor: "#e5adb2"
    };
  }
};

const sizeChange = pageSize => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = pageNum => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const getStatusText = orderStatus => {
  if (orderStatus == -1) {
    return "PAUSED";
  } else if (orderStatus == 0) {
    return "RUNNING";
  } else if (orderStatus == 1) {
    return "FINISHED";
  } else if (orderStatus == -2) {
    return "STOPPED";
  } else if (orderStatus == -3) {
    return "ERROR";
  }
};

const getProgressStatus = orderStatus => {
  if (orderStatus == -1) {
    return "warning";
  } else if (orderStatus == 0) {
    return "primary";
  } else if (orderStatus == 1) {
    return "success";
  } else if (orderStatus == -2) {
    return "exception";
  } else if (orderStatus == -3) {
    return "exception";
  }
};

const updateLastOrder = () => {
  let lastOrder = useAcrStoreHook().getLastOrder;
  if (pageData.searchInfo.pageNum != 1) {
    return;
  }

  for (let i = lastOrder.length - 1; i >= 0; i--) {
    let order = lastOrder[i];
    let idx = pageData.dataList.findIndex(
      item => item.orderId == order.orderId
    );
    if (idx >= 0) {
      pageData.dataList[idx] = order;
    } else {
      if (pageData.dataList.length >= pageData.searchInfo.pageSize) {
        pageData.dataList.pop();
      }
      pageData.dataList.unshift(order);
      pageData.searchInfo.total += 1;
    }
  }
};

defineExpose({
  updateLastOrder
});
</script>

<template>
  <el-row align="middle">
    <el-col align="center">
      <el-table
        v-loading="pageData.loading"
        :data="pageData.dataList"
        style="width: 95%"
        row-key="orderId"
        :row-style="rowState"
        border
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column
          prop="orderId"
          label="ID"
          :show-overflow-tooltip="true"
          min-width="15%"
        />
        <el-table-column
          prop="acrId"
          label="ACR"
          :show-overflow-tooltip="true"
          min-width="5%"
        />
        <el-table-column
          prop="orderInfo"
          label="DISPATCH INFO"
          resizable
          :show-overflow-tooltip="true"
          min-width="40%"
        />
        <el-table-column
          label="STATUS"
          resizable
          :show-overflow-tooltip="true"
          min-width="10%"
        >
          <template v-slot="scope">
            <div
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-badge
                :value="getStatusText(scope.row.orderStatus)"
                :type="getProgressStatus(scope.row.orderStatus)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="PROGRESS"
          :show-overflow-tooltip="true"
          min-width="30%"
        >
          <template v-slot="scope">
            <el-progress
              :text-inside="true"
              :stroke-width="24"
              :percentage="
                Math.round((scope.row.taskProgress / scope.row.taskTotal) * 100)
              "
              :status="getProgressStatus(scope.row.orderStatus)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
  <el-row style="margin-bottom: 1rem; margin-top: 0.5rem">
    <el-col>
      <el-pagination
        v-model:currentPage="pageData.searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
.el-col {
  display: flex;
  justify-content: center;
}

.el-badge {
  margin-left: 0;
  margin-top: 0.5rem;
  padding: 0;
}
</style>
