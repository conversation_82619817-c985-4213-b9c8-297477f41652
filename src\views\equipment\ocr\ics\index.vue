<script setup lang="ts">
import { ocrIcsTxtAPI } from "/@/api/equipment/ocrIcsTxt";
import { onMounted, reactive } from "vue";
import {
  TesterSelectVO,
  paramList
} from "/@/api/model/equipment/ocr_ics_txt_model";
import chart1 from "./chart1.vue";
import chart2 from "./chart2.vue";
import chart3 from "./chart3.vue";
import { TesterGroupVO } from "/@/api/model/equipment/equipment_model";
import { transformI18n } from "/@/plugins/i18n";

defineOptions({
  name: "icsTxtStatistic"
});

const pageData = reactive<{
  ChartComponentKey1: number;
  ChartComponentKey2: number;
  ChartComponentKey3: number;
  testers: TesterSelectVO[];
  searchTestInfo: paramList;
  equipGroupList: TesterGroupVO[];
}>({
  ChartComponentKey1: 1,
  ChartComponentKey2: 1,
  ChartComponentKey3: 1,
  testers: [],
  searchTestInfo: {
    groupId: null,
    testTime: null,
    tester: null,
    position: "1",
    paramType: "ED1"
  },
  equipGroupList: []
});

onMounted(() => {
  pageData.searchTestInfo.testTime = getTodayDate(new Date());
  getGroupList();
});

const getGroupList = () => {
  const param: { testTime: String } = {
    testTime: pageData.searchTestInfo.testTime
  };
  ocrIcsTxtAPI
    .getGroupSelectLists(param)
    .then((res: TesterGroupVO[] | string) => {
      if (res !== "fail") {
        pageData.equipGroupList = res as TesterGroupVO[];
        if (pageData.equipGroupList.length == 0) {
          pageData.equipGroupList = [];
          pageData.testers = [];
          return;
        }
        pageData.searchTestInfo.groupId = pageData.equipGroupList[0].id;
        getTesterSelectList();
      }
    });
};

let getTesterSelectList = () => {
  const param: { groupId: String } = {
    groupId: pageData.searchTestInfo.groupId
  };
  ocrIcsTxtAPI

    .getTesterSelectList(param)
    .then((res: TesterSelectVO[] | string) => {
      if (res !== "fail") {
        pageData.testers = res as TesterSelectVO[];
        if (pageData.testers.length > 0) {
          pageData.searchTestInfo.tester = pageData.testers[0].testerName;
          handlerSearch();
        }
      }
    });
};

const getTodayDate = (date: Date) => {
  let month: string | number = date.getMonth() + 1;
  let strDate: string | number = date.getDate();

  if (month <= 9) {
    month = "0" + month;
  }

  if (strDate <= 9) {
    strDate = "0" + strDate;
  }

  return date.getFullYear() + "-" + month + "-" + strDate;
};

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const handlerSearch = () => {
  pageData.ChartComponentKey1 += 1;
  pageData.ChartComponentKey2 += 1;
  pageData.ChartComponentKey3 += 1;
};
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-date-picker
          v-model="pageData.searchTestInfo.testTime"
          type="date"
          placeholder="日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          :clearable="false"
          @change="getGroupList"
        />
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.groupId"
          :placeholder="transformI18n('equipment.groupName')"
          @change="getTesterSelectList"
          :clearable="false"
        >
          <el-option
            v-for="item in pageData.equipGroupList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.tester"
          @change="handlerSearch"
          placeholder="设备"
        >
          <el-option
            v-for="item in pageData.testers"
            :key="item.id"
            :label="item.testerName"
            :value="item.testerName"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.paramType"
          placeholder="参数"
          @change="handlerSearch"
        >
          <el-option
            v-for="item in ocrIcsTxtAPI.ParamType"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select
          v-model="pageData.searchTestInfo.position"
          placeholder="位置"
          clearable
          @change="handlerSearch"
        >
          <el-option
            v-for="item in ocrIcsTxtAPI.posList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
    </el-form>

    <el-card>
      <template #default>
        <chart1
          :txt="pageData.searchTestInfo"
          :key="pageData.ChartComponentKey1"
        />
      </template>
    </el-card>
    <el-card>
      <template #default>
        <chart2
          :txt="pageData.searchTestInfo"
          :key="pageData.ChartComponentKey2"
        />
      </template>
    </el-card>
    <el-card>
      <template #default>
        <chart3
          :txt="pageData.searchTestInfo"
          :key="pageData.ChartComponentKey3"
        />
      </template>
    </el-card>
  </div>
</template>

<style scoped></style>
