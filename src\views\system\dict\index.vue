<script setup lang="ts">
import { dictApi } from "/@/api/system/dict/dict";
import { onMounted, reactive } from "vue";
import { Dict, DictQuery, DictEntry } from "/@/api/model/system/dict_model";
import { Page } from "/@/api/model/domain";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";

defineOptions({
  name: "EquipmentManage"
});

const pageData = reactive<{
  loading: boolean;
  testList: Dict[];
  searchTestInfo: DictQuery;
}>({
  loading: false,
  testList: [],
  searchTestInfo: {
    name: "",
    type: ""
  }
});

const getPage = () => {
  pageData.loading = true;
  dictApi
    .page(pageData.searchTestInfo)
    .then((res: Page<Dict[]> | string) => {
      if (res !== "fail") {
        pageData.testList = (res as Page<Dict[]>).records;
        pageData.searchTestInfo.total = Number((res as Page<Dict[]>).total);
      }
    })
    .finally(() => (pageData.loading = false));
};

onMounted(() => {
  console.log(ittt.save);
  getPage();
});

const handlerSearch = () => {
  pageData.searchTestInfo.pageNum = 1;
  getPage();
};

const sizeChange = (pageSize: number) => {
  pageData.searchTestInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchTestInfo.pageNum = pageNum;
  getPage();
};

const onAddItem = (vo: Dict) => {
  let entry: DictEntry = {
    id: null,
    parentId: vo.id,
    description: "",
    name: "",
    value: "",
    sort: vo.entry.length + 1,
    isSave: true
  };
  vo.entry.push(entry);
};

const deleteEntryById = (vo: DictEntry) => {
  ElMessageBox.confirm("Are you delete batch?")
    .then(() => {
      dictApi.deleteEntryById(vo.id).then(() => {
        updateDictData(vo.parentId);
      });
    })
    .catch(() => {
      // catch error
    });
};

const toUpdate = (vo: DictEntry) => {
  vo.isSave = true;
};

const saveEntry = (vo: DictEntry) => {
  ElMessageBox.confirm("Are you sure to submit?")
    .then(() => {
      dictApi.saveEntry(vo).then((res: string) => {
        if (res !== "fail") {
          vo.isSave = false;
          updateDictData(vo.parentId);
        }
      });
    })
    .catch(() => {
      // catch error
    });
};

const updateDictData = (id: string) => {
  dictApi.getEntityById(id).then((res: Dict | string) => {
    if (res !== "fail") {
      for (var i in pageData.testList) {
        if (pageData.testList[i].id == id) {
          pageData.testList[i] = res as Dict;
        }
      }
    }
  });
};

const ittt = reactive<{
  save: string;
}>({
  save: transformI18n("buttons.hssave")
});
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-form-item>
        <el-input
          v-model="pageData.searchTestInfo.name"
          placeholder="名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="pageData.searchTestInfo.type"
          placeholder="类型"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button plain @click="handlerSearch">Query</el-button>
      </el-form-item>
    </el-form>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        :border="true"
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-table
              :data="props.row.entry"
              :border="true"
              row-key="id"
              style="width: 80%; margin: auto"
            >
              <el-table-column label="sort" prop="sort" width="80px">
                <template #default="scope">
                  <el-input v-model="scope.row.sort" v-if="scope.row.isSave" />
                  <span v-else>{{ scope.row.sort }}</span>
                </template>
              </el-table-column>
              <el-table-column label="name" prop="name">
                <template #default="scope">
                  <el-input v-model="scope.row.name" v-if="scope.row.isSave" />
                  <span v-else>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="value" prop="value" width="80px">
                <template #default="scope">
                  <el-input v-model="scope.row.value" v-if="scope.row.isSave" />
                  <span v-else>{{ scope.row.value }}</span>
                </template>
              </el-table-column>
              <el-table-column label="description" prop="description">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.description"
                    v-if="scope.row.isSave"
                  />
                  <span v-else>{{ scope.row.description }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="Operations" width="120px">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="deleteEntryById(scope.row)"
                    >delete</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    size="small"
                    v-if="!scope.row.isSave"
                    @click="toUpdate(scope.row)"
                    >update</el-button
                  ><el-button
                    link
                    type="primary"
                    size="small"
                    v-if="scope.row.isSave || false"
                    @click="saveEntry(scope.row)"
                    >{{ ittt.save }}</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <div style="text-align: center">
              <el-button
                class="mt-4"
                style="width: 80%"
                @click="onAddItem(props.row)"
                >Add Item</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="type"
          label="类型"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="description"
          label="描述"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchTestInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchTestInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchTestInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped></style>
