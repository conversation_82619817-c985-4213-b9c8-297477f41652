<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, reactive } from "vue";
import { icsAutoAdjustAPI } from "/@/api/local/icsAutoAdjust";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const manuelData = reactive({
  gt1: {
    value: "",
    focus: false
  },
  ex1: {
    value: "",
    focus: false
  },
  ey1: {
    value: "",
    focus: false
  },
  gt2: {
    value: "",
    focus: false
  },
  ex2: {
    value: "",
    focus: false
  },
  ey2: {
    value: "",
    focus: false
  },
  gtFormula1: {
    value: "",
    focus: false
  },
  gtFormula2: {
    value: "",
    focus: false
  },
  exFormula1: {
    value: "",
    focus: false
  },
  eyFormula1: {
    value: "",
    focus: false
  },
  exFormula2: {
    value: "",
    focus: false
  },
  eyFormula2: {
    value: "",
    focus: false
  }
});

const selectedValue = ref("");

let timer = null;

onMounted(() => {
  selectedValue.value = "ics_l";
  timer = setInterval(() => {
    fetchPlcData();
  }, 2000);
});

const clearTimer = () => {
  clearInterval(timer);
  timer = null;
};

onBeforeUnmount(() => {
  clearTimer();
});

const fetchPlcData = () => {
  icsAutoAdjustAPI
    .fetchManuelData({
      equipId: props.icsEquipInfo[selectedValue.value].id
    })
    .then(res => {
      if (res !== "fail") {
        if (!manuelData.gt1.focus) {
          manuelData.gt1.value = res.gt1 === undefined ? "" : res.gt1;
        }
        if (!manuelData.ex1.focus) {
          manuelData.ex1.value = res.ex1 === undefined ? "" : res.ex1;
        }
        if (!manuelData.ey1.focus) {
          manuelData.ey1.value = res.ey1 === undefined ? "" : res.ey1;
        }
        if (!manuelData.gt2.focus) {
          manuelData.gt2.value = res.gt2 === undefined ? "" : res.gt2;
        }
        if (!manuelData.ex2.focus) {
          manuelData.ex2.value = res.ex2 === undefined ? "" : res.ex2;
        }
        if (!manuelData.ey2.focus) {
          manuelData.ey2.value = res.ey2 === undefined ? "" : res.ey2;
        }
        if (!manuelData.gtFormula1.focus) {
          manuelData.gtFormula1.value = res.gtFormula1;
        }
        if (!manuelData.gtFormula2.focus) {
          manuelData.gtFormula2.value = res.gtFormula2;
        }
        if (!manuelData.exFormula1.focus) {
          manuelData.exFormula1.value = res.exFormula1;
        }
        if (!manuelData.eyFormula1.focus) {
          manuelData.eyFormula1.value = res.eyFormula1;
        }
        if (!manuelData.exFormula2.focus) {
          manuelData.exFormula2.value = res.exFormula2;
        }
        if (!manuelData.eyFormula2.focus) {
          manuelData.eyFormula2.value = res.eyFormula2;
        }
      }
    })
    .catch(err => {
      console.log(err);
    });
};

const writeData = parameter => {
  ElMessageBox({
    title: "Please Confirm Write!",
    message:
      "Write " +
      parameter.toUpperCase() +
      " value:" +
      manuelData[parameter].value,
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      icsAutoAdjustAPI
        .writePlcData({
          equipId: props.icsEquipInfo[selectedValue.value].id,
          parameter: parameter,
          value: manuelData[parameter].value
        })
        .then(res => ElMessageBox.alert(res))
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          manuelData[parameter].focus = false;
        });
    })
    .catch(() => (manuelData[parameter].focus = false));
};

const saveFormula = parameter => {
  ElMessageBox({
    title: "Please Confirm Update!",
    message: "Update Formula to: Y=" + manuelData[parameter].value,
    confirmButtonText: transformI18n("dialogue.confirm-yes"),
    cancelButtonText: transformI18n("dialogue.confirm-no"),
    showCancelButton: true
  })
    .then(() => {
      icsAutoAdjustAPI
        .updateFormula({
          equipId: props.icsEquipInfo[selectedValue.value].id,
          parameter: parameter,
          formula: manuelData[parameter].value
        })
        .then(res => ElMessageBox.alert(res))
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          manuelData[parameter].focus = false;
        });
    })
    .catch(() => (manuelData[parameter].focus = false));
};

const setFocus = parameter => {
  manuelData[parameter].focus = true;
};
const resetFocus = parameter => {
  manuelData[parameter].focus = false;
};
</script>

<template>
  <el-descriptions border size="large" :column="1" style="margin-top: 5px">
    <el-descriptions-item label="Manual" label-align="center" align="left">
      <el-row align="middle">
        Select ICS L/R:
        <el-select
          v-model="selectedValue"
          placeholder="Select L/R"
          :clearable="false"
        >
          <el-option label="ICS-L" value="ics_l" />
          <el-option label="ICS-R" value="ics_r" />
        </el-select>
      </el-row>
      <el-row>
        <el-col :span="8">
          GlueTime1:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gt1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gt1')"
                @clear="resetFocus('gt1')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('gt1')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
        <el-col :span="8">
          EX1:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.ex1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('ex1')"
                @clear="resetFocus('ex1')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('ex1')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
        <el-col :span="8">
          EY1:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.ey1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('ey1')"
                @clear="resetFocus('ey1')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('ey1')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          GlueTime2:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gt2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gt2')"
                @clear="resetFocus('gt2')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('gt2')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
        <el-col :span="8">
          EX2:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.ex2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('ex2')"
                @clear="resetFocus('ex2')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('ex2')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
        <el-col :span="8">
          EY2:
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.ey2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('ey2')"
                @clear="resetFocus('ey2')" /></el-col
            ><el-col :span="1" /><el-col :span="8"
              ><el-button
                type="primary"
                @click="writeData('ey2')"
                v-auth="['autoics:update']"
                >Write</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          GlueTime1 Adjust Formula(x=ED's Target - ▲ED, ▲ED equals to rule
          triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula1')"
                @clear="resetFocus('gtFormula1')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula1')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          GlueTime2 Adjust Formula(x=ED's Target - ▲ED, ▲ED equals to rule
          triggering point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.gtFormula2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('gtFormula2')"
                @clear="resetFocus('gtFormula2')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('gtFormula2')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          EX1 Adjust Formula(x=EX's Target - ▲EX, ▲EX equals to rule triggering
          point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.exFormula1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('exFormula1')"
                @clear="resetFocus('exFormula1')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('exFormula1')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          EY1 Adjust Formula(x=EY's Target - ▲EY, ▲EY equals to rule triggering
          point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.eyFormula1.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('eyFormula1')"
                @clear="resetFocus('eyFormula1')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('eyFormula1')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          EX2 Adjust Formula(x=EX's Target - ▲EX, ▲EX equals to rule triggering
          point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.exFormula2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('exFormula2')"
                @clear="resetFocus('exFormula2')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('exFormula2')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          EY2 Adjust Formula(x=EY's Target - ▲EY, ▲EY equals to rule triggering
          point):
          <el-row align="middle">
            <el-col :span="12">
              <el-input
                v-model="manuelData.eyFormula2.value"
                placeholder="Empty"
                :clearable="true"
                @focus="setFocus('eyFormula2')"
                @clear="resetFocus('eyFormula2')" /></el-col
            ><el-col :span="1" /><el-col :span="3"
              ><el-button
                type="primary"
                @click="saveFormula('eyFormula2')"
                v-auth="['autoics:update']"
                >Apply</el-button
              ></el-col
            >
          </el-row>
        </el-col>
      </el-row>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped></style>
