import { BaseClass, BaseQuery } from "../domain";

export interface User extends BaseClass {
  id: string;
  username: string;
  engName: string;
  zhName: string;
  title: string;
  email: string;
  state: number;
  isAdmin: number;
  deptId: string;
  roleId: string;
  description: string;
}

export interface UserInfo extends BaseClass {
  /**
   * id
   */
  id: string;
  /**
   * 用户账号
   */
  username: string;
  /**
   * 昵称
   */
  engName: string;

  zhName: string;

  avatar: string;

  title: string;

  email: string;
}

export interface UserQuery extends BaseQuery {
  deptId?: string;
  nickName?: string;
  username?: string;
  isEnabled?: number;
}
