<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    @close="props.closeCallBack"
    size="50%"
  >
    <template #header>
      <h4>
        {{
          transformI18n("equipment.settingParameter") +
          ": " +
          props.entity.equipmentName
        }}
      </h4>
    </template>
    <template #default>
      <div>
        <el-table :data="pageData.parameterList" :border="true" row-key="id">
          <el-table-column
            :label="transformI18n('equipment.parameterName')"
            prop="parameterName"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.parameterName"
                v-if="scope.row.isSave"
              />
              <span v-else>{{ scope.row.parameterName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="transformI18n('equipment.idealValue')"
            prop="idealValue"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.idealValue"
                v-if="scope.row.isSave"
              />
              <span v-else>{{ scope.row.idealValue }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="transformI18n('equipment.maxValue')"
            prop="maxValue"
          >
            <template #default="scope">
              <el-input v-model="scope.row.maxValue" v-if="scope.row.isSave" />
              <span v-else>{{ scope.row.maxValue }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="transformI18n('equipment.minValue')"
            prop="minValue"
          >
            <template #default="scope">
              <el-input v-model="scope.row.minValue" v-if="scope.row.isSave" />
              <span v-else>{{ scope.row.minValue }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="transformI18n('equipment.description')"
            prop="description"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.description"
                v-if="scope.row.isSave"
              />
              <span v-else>{{ scope.row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            :label="transformI18n('buttons.hsoperations')"
            width="120px"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="deleteById(scope.row.id)"
                >{{ transformI18n("buttons.hsdelete") }}</el-button
              >
              <el-button
                link
                type="primary"
                size="small"
                v-if="!scope.row.isSave"
                @click="toUpdate(scope.row)"
                >{{ transformI18n("buttons.hsupdate") }}</el-button
              ><el-button
                link
                type="primary"
                size="small"
                v-if="scope.row.isSave || false"
                @click="saveEntry(scope.row)"
                >{{ transformI18n("buttons.hssave") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button style="width: 100%" @click="onAddItem()">{{
          transformI18n("buttons.hsadd")
        }}</el-button>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">{{
          transformI18n("buttons.hscancel")
        }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { ElMessageBox } from "element-plus";
import { equipParameterAPI } from "/@/api/equipment/equipParameter";
import {
  Equipment,
  EquipParameterVo
} from "/@/api/model/equipment/equipment_model";
import { transformI18n } from "/@/plugins/i18n";

const drawer = ref(true);

onMounted(() => {
  getParameterList();
});

const props = defineProps({
  entity: {
    type: Object as PropType<Equipment>,
    default: null
  },
  closeCallBack: {
    type: Function
  }
});

const pageData = reactive<{
  parameterList: EquipParameterVo[];
}>({
  parameterList: []
});

const getParameterList = () => {
  equipParameterAPI
    .getList(props.entity.id)
    .then((res: EquipParameterVo[] | string) => {
      if (res !== "fail") {
        pageData.parameterList = res as EquipParameterVo[];
      }
    });
};

function cancelClick() {
  drawer.value = false;
}

const onAddItem = () => {
  let entry: EquipParameterVo = {
    id: null,
    parameterName: null,
    equipmentId: props.entity.id,
    idealValue: 0.0,
    maxValue: 0.0,
    minValue: 0.0,
    description: null,
    isSave: true
  };
  pageData.parameterList.push(entry);
};

const saveEntry = (vo: EquipParameterVo) => {
  equipParameterAPI.save(vo).then((res: string) => {
    if (res !== "fail") {
      vo.isSave = false;
      getParameterList();
    }
  });
};

const toUpdate = (vo: EquipParameterVo) => {
  vo.isSave = true;
};

const deleteById = (id: string) => {
  if (!id) {
    return;
  }
  ElMessageBox.confirm("Are you delete batch?")
    .then(() => {
      equipParameterAPI.deleteById(id).then(() => {
        getParameterList();
      });
    })
    .catch(() => {
      // catch error
    });
};
</script>
