<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";
// 定义 props 类型

const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array,
    required: true
  },
  yLine: {
    type: Array,
    required: true
  },
  yAxisMin: {
    type: Number,
    default: 0
  },
  yAxisMax: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    required: true
  },
  loading: {
    type: <PERSON><PERSON>an,
    default: false
  }
});
let chart: echarts.ECharts | null = null; // 存储ECharts实例;
// let yAxisMin = 0;
// let yAxisMax = 0;

// // 计算 y 轴最小值和最大值
// const calculateYAxisRange = () => {
//   const allData = props.yLine.flatMap((series: any) => series.data);
//   if (allData.length > 0) {
//     yAxisMin = Math.min(...allData);
//     yAxisMax = Math.max(...allData);
//   } else {
//     yAxisMin = 0;
//     yAxisMax = 0;
//   }
// };

const initChart = () => {
  if (!chart) return;
  if (props.loading) {
    setTimeout(() => {
      chart.showLoading();
    }, 50);
  }
  console.log("12345");
  //calculateYAxisRange();
  let option: EChartsOption;
  option = {
    title: {
      text: props.title, // 标题内容
      left: "center", // 将标题居中
      //top: "0%", // 距顶部距离
      textStyle: {
        color: "#333", // 标题颜色
        fontSize: 20, // 标题字体大小
        fontWeight: "bold", // 加粗字体
        fontStyle: "italic", // 斜体副标题
        fontFamily: "Arial, sans-serif", // 字体
        textBorderColor: "rgba(255, 255, 255, 0.2)", // 标题边框颜色
        textBorderWidth: 0, // 标题边框宽度
        shadowColor: "rgba(0, 0, 0, 0.2)" // 标题阴影颜色
      },
      backgroundColor: "rgba(255, 255, 255, 0.2)", // 标题背景色（带透明度）
      borderRadius: 10, // 圆角背景
      padding: [10, 15], // 内边距
      borderColor: "rgba(255, 255, 255, 0.2)", // 边框颜色
      borderWidth: 1 // 边框宽度
    },
    backgroundColor: "#fff", // 设置图表背景颜色为白色
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: props.xLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)", // 背景透明度
      borderColor: "#333", // 边框颜色
      borderWidth: 1,
      textStyle: {
        color: "#fff", // 字体颜色
        fontSize: 12 // 字体大小
      },
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(0,0,0,0.1)" // 指示线的阴影效果
        }
      },
      formatter: function (params) {
        let result = `<div style="text-align: left;">${params[0].axisValue}<br/>`;
        params.forEach(item => {
          let unit = ""; // 默认单位为空
          if (item.seriesName.includes("T")) {
            unit = "°C";
          } else if (item.seriesName.includes("F")) {
            unit = "L/min";
          } else if (item.seriesName.includes("N2")) {
            unit = "%";
          }
          result += `
        <span style="display:inline-block;width:10px;height:10px;border-radius:100%;background : ${
          item.color
        };margin-right:5px;"></span>
         ${item.seriesName} : ${parseFloat(item.data).toFixed(3)} ${unit}<br/>
      `;
        });
        result += "</div>";
        return result;
      }
    },
    legend: {
      top: "8%",
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      itemGap: 10 // 图例项之间的间距
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        saveAsImage: {}
      }
    },
    grid: {
      top: "22%",
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xLine
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        min: null, //yAxisMin == 0 ? 0 : yAxisMin + yAxisMin*0.1,
        max: null //yAxisMax == 0 ? 0 : yAxisMax + yAxisMax*0.1
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 15,
        handleSize: "150%", // 增加手柄的大小
        handleStyle: {
          color: "#fff", // 手柄的颜色
          borderColor: "#5D92F4" // 手柄的边框颜色
        },
        textStyle: {
          color: "#5D92F4" // 滚动条上的文字颜色
        },
        borderColor: "#5D92F4", // 滚动条边框颜色
        fillerColor: "rgba(93, 146, 244, 0.5)", // 选中区域的颜色
        backgroundColor: "rgba(221, 221, 221, 0.2)" // 未选中区域的背景色
      }
    ],
    series: props.yLine
  };
  console.log(option);
  option && chart.setOption(option);
};

onMounted(() => {
  const chartElement = document.getElementById(props.chartId) as HTMLElement;
  if (chartElement) {
    chart = echarts.init(chartElement);
    initChart();
  }
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine
  }),
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);
watch(
  () => props.loading,
  status => {
    if (status) {
      // 显示加载动画
      if (chart) {
        setTimeout(() => {
          chart.showLoading();
        }, 50);
      }
    } else {
      // 隐藏加载动画
      if (chart) {
        setTimeout(() => {
          chart.hideLoading();
        }, 50);
      }
    }
  }
);
onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div :id="chartId" class="chart-container" />
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 这里可以根据需要调整高度 */
}
</style>
