<script setup lang="ts">
import { icsAutoAdjustAPI } from "/@/api/local/icsAutoAdjust";
import { onMounted, reactive, onBeforeUnmount } from "vue";

const currentStatus = reactive({
  clientStatus: false,
  kv8000Status: false,
  scadaStatus: false,
  icsLLogStatus: false,
  icsRLogStatus: false
});

let timer = null;

onMounted(() => {
  fetchStatus();
  timer = setInterval(() => {
    fetchStatus();
  }, 10000);
});

const fetchStatus = () => {
  icsAutoAdjustAPI
    .fetchStatus()
    .then((res: any) => {
      if (res !== "fail") {
        currentStatus.clientStatus = res.clientStatus === "1";
        currentStatus.kv8000Status = res.kv8000Status === "1";
        currentStatus.scadaStatus = res.scadaStatus === "1";
        currentStatus.icsLLogStatus = res.icsLLogStatus === "1";
        currentStatus.icsRLogStatus = res.icsRLogStatus === "1";
      }
    })
    .catch(_err => {
      console.log("error fetching status:" + _err);
    });
};

onBeforeUnmount(() => {
  clearInterval(timer);
  timer = null;
});
</script>

<template>
  <el-descriptions
    border
    size="large"
    :column="4"
    direction="vertical"
    style="margin-top: 10px"
  >
    <el-descriptions-item
      label="HDS Client"
      label-align="center"
      align="center"
      width="25%"
    >
      <el-badge
        :value="currentStatus.clientStatus ? 'Online' : 'OffLine'"
        :type="currentStatus.clientStatus ? 'success' : 'danger'"
      >
        <IconifyIconOffline class="equip-icon" icon="monitor" />
      </el-badge>
    </el-descriptions-item>
    <el-descriptions-item
      label="PLC"
      label-align="center"
      align="center"
      width="25%"
    >
      <el-badge
        :value="
          currentStatus.kv8000Status
            ? 'Online'
            : currentStatus.scadaStatus
            ? 'Online'
            : 'OffLine'
        "
        :type="
          currentStatus.kv8000Status
            ? 'success'
            : currentStatus.scadaStatus
            ? 'success'
            : 'danger'
        "
      >
        <IconifyIconOffline
          icon="card"
          :style="{ width: '100%', height: '100%' }"
        />
      </el-badge>
    </el-descriptions-item>
    <el-descriptions-item
      label="ICS-L"
      label-align="center"
      align="center"
      width="25%"
    >
      <el-badge
        :value="currentStatus.icsLLogStatus ? 'Online' : 'OffLine'"
        :type="currentStatus.icsLLogStatus ? 'success' : 'danger'"
      >
        <IconifyIconOffline
          icon="monitor"
          :style="{ width: '100%', height: '100%' }"
        />
      </el-badge>
    </el-descriptions-item>
    <el-descriptions-item
      label="ICS-R"
      label-align="center"
      align="center"
      width="25%"
    >
      <el-badge
        :value="currentStatus.icsRLogStatus ? 'Online' : 'OffLine'"
        :type="currentStatus.icsRLogStatus ? 'success' : 'danger'"
      >
        <IconifyIconOffline
          icon="monitor"
          :style="{ width: '100%', height: '100%' }"
        />
      </el-badge>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped>
.el-badge {
  width: 30%;
  height: 30%;
  margin-top: 10%;
}

.equip-icon {
  width: 100%;
  height: 100%;
}
</style>
