{"name": "hds-admin", "version": "0.0.1", "private": true, "scripts": {"dev": "cross-env --max_old_space_size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && cross-env vite build", "build:staging": "rimraf dist && cross-env vite build --mode staging", "report": "rimraf dist && cross-env vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "cloc": "cross-env --max_old_space_size=4096 cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rm -rf node_modules && rm -rf .eslintcache && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,css,scss,postcss,less}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@stomp/stompjs": "^7.0.0", "@vueuse/core": "^8.9.4", "@vueuse/motion": "^2.0.0-beta.12", "@wangeditor/editor": "^5.1.10", "animate.css": "^4.1.1", "axios": "^0.27.2", "china-area-data": "^5.0.1", "cropperjs": "^1.5.12", "css-color-function": "^1.3.3", "dayjs": "^1.11.3", "driver.js": "^0.9.8", "echarts": "^5.3.3", "element-plus": "^2.2.9", "element-resize-detector": "^1.2.4", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "lowdb": "^3.0.0", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.0.16", "pixijs": "^7.1.4", "qrcode": "^1.5.1", "qs": "^6.11.0", "resize-observer-polyfill": "^1.5.1", "responsive-storage": "^2.0.0", "rgb-hex": "^4.0.0", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "swiper": "^8.3.1", "v-contextmenu": "3.0.0", "vue": "^3.2.37", "vue-form-create2": "^1.2.9", "vue-i18n": "^9.2.0-beta.39", "vue-json-pretty": "^2.1.1", "vue-pdf-embed": "^1.1.4", "vue-router": "^4.1.2", "vue-types": "^4.1.1", "vue-virtual-scroller": "^2.0.0-alpha.1", "vuedraggable": "4.1.0", "websocket": "^1.0.34"}, "devDependencies": {"@commitlint/cli": "13.1.0", "@commitlint/config-conventional": "13.1.0", "@faker-js/faker": "^6.3.1", "@iconify-icons/carbon": "^1.2.7", "@iconify-icons/ep": "^1.2.7", "@iconify-icons/fa": "^1.2.3", "@iconify-icons/fa-solid": "^1.2.3", "@iconify-icons/fluent": "^1.2.15", "@iconify-icons/mdi": "^1.2.27", "@iconify-icons/ri": "^1.2.3", "@iconify-icons/uil": "^1.2.2", "@iconify/vue": "^3.2.1", "@intlify/vite-plugin-vue-i18n": "^5.0.0", "@pureadmin/theme": "^2.4.0", "@types/element-resize-detector": "1.1.3", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@types/mockjs": "1.0.3", "@types/node": "14.14.14", "@types/nprogress": "0.2.0", "@types/qrcode": "^1.4.2", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue": "^3.0.1", "@vitejs/plugin-vue-jsx": "^2.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vue/runtime-core": "^3.2.37", "autoprefixer": "^10.4.7", "cloc": "^2.10.0", "cross-env": "7.0.3", "eslint": "^8.20.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "font-awesome": "^4.7.0", "husky": "^7.0.4", "lint-staged": "11.1.2", "picocolors": "^1.0.0", "postcss": "^8.4.14", "postcss-html": "^1.5.0", "postcss-import": "14.0.0", "postcss-scss": "^4.0.4", "prettier": "^2.7.1", "pretty-quick": "3.1.1", "rimraf": "3.0.2", "rollup-plugin-visualizer": "^5.7.1", "sass": "^1.53.0", "sass-loader": "^13.0.2", "stylelint": "^14.9.1", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^6.0.0", "stylelint-config-standard": "^24.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.7.4", "unocss": "^0.44.3", "unplugin-vue-define-options": "^0.6.2", "vite": "^3.0.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-remove-console": "^1.1.0", "vite-svg-loader": "^3.4.0", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^1.0.10"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "terser", "webpack"]}}, "__npminstall_done": false}