<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";

// 定义 props 类型
const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array,
    required: true
  },
  yLine: {
    type: Array,
    required: true
  },
  yAxisMin: {
    type: Number,
    default: 0
  },
  yAxisMax: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    required: true
  },
  alarmLine: {
    type: Number,
    default: 0
  }
});
let chart: echarts.ECharts | null = null; // 存储ECharts实例;
//let yAxisMin = 0;
//let yAxisMax = 0;

//计算 y 轴最小值和最大值
// const calculateYAxisRange = () => {
//   const allData = props.yLine.flatMap((series: any) => series.data);
//   if (allData.length > 0) {
//     //yAxisMin = Math.min(...allData);
//     yAxisMax = Math.max(...allData);
//   } else {
//     //yAxisMin = 0;
//     yAxisMax = 0;
//   }
// };

const initChart = () => {
  if (!chart) return;
  chart.clear(); // 清除之前的图表

  // 定义默认颜色数组
  const defaultColors = [
    "#5793f3", // 蓝色
    "#ff9f7f", // 橙色
    "#675bba", // 紫色
    "#d14a61", // 红色
    "#75bedc", // 青色
    "#fec42c", // 黄色
    "#83d976", // 绿色
    "#ff6a00" // 橙色
  ];

  // 计算 legend 项目数量，动态调整 grid top 值
  const legendItemCount = props.yLine.length;
  // 估算需要的行数（假设每行最多显示8个项目）
  const estimatedRows = Math.ceil(legendItemCount / 8);
  // 根据行数动态调整 grid top 值，每行大约需要 3% 的空间
  const dynamicGridTop = Math.max(20, 15 + estimatedRows * 3);

  // const visualMapPieces = [];
  // if (props.title.includes("Yield")) {
  //   props.yLine.forEach((seriesData, seriesIndex) => {
  //     if (typeof seriesData === "object" && seriesData !== null) {
  //       visualMapPieces.push({
  //         show: false,
  //         seriesIndex: seriesIndex,
  //         dimension: 1,
  //         pieces: [
  //           {
  //             lte: props.alarmLine,
  //             gte: 0,
  //             color: defaultColors[seriesIndex % defaultColors.length]
  //           },
  //           {
  //             gt: props.alarmLine,
  //             lte: 100,
  //             color: defaultColors[seriesIndex % defaultColors.length]
  //           }
  //         ]
  //       });
  //     }
  //   });
  // } else {
  //   props.yLine.forEach((seriesData, seriesIndex) => {
  //     if (typeof seriesData === "object" && seriesData !== null) {
  //       visualMapPieces.push({
  //         show: false,
  //         seriesIndex: seriesIndex,
  //         dimension: 1,
  //         pieces: [
  //           {
  //             lte: props.alarmLine,
  //             gte: 0,
  //             color: defaultColors[seriesIndex % defaultColors.length]
  //           },
  //           {
  //             gt: props.alarmLine,
  //             lte: 100,
  //             color: defaultColors[seriesIndex % defaultColors.length]
  //           }
  //         ]
  //       });
  //     }
  //   });
  // }

  //calculateYAxisRange();
  let option: EChartsOption;
  const legendData = props.yLine.map((seriesData: { name: string }, index) => {
    return {
      name: seriesData.name,
      icon: "circle", // 图例图标类型
      itemStyle: {
        color: defaultColors[index % defaultColors.length]
      },
      textStyle: {
        color: defaultColors[index % defaultColors.length]
      }
    };
  });
  option = {
    // 全局性能优化配置
    animation: props.xLine.length > 1000 ? false : true, // 大数据量时关闭动画
    animationDuration: 1000,
    animationEasing: "cubicOut",

    title: {
      text: props.title, // 标题内容
      left: "center", // 将标题居中
      //top: "2%", // 距顶部距离
      //bottom: "2%", //底部距离
      textStyle: {
        color: "black", // 标题颜色
        fontSize: 18, // 标题字体大小
        fontWeight: "bold", // 加粗字体
        fontStyle: "normal", // 正楷
        fontFamily: "Arial, sans-serif", // 字体
        textBorderColor: "rgba(255, 255, 255, 0.6)", // 标题边框颜色
        textBorderWidth: 0, // 标题边框宽度
        shadowColor: "rgba(0, 0, 0, 0.6)" // 标题阴影颜色
      },
      //backgroundColor: "rgba(245, 245, 245, 0.6)", // 标题背景色（带透明度）
      borderRadius: 10, // 圆角背景
      padding: [10, 15], // 内边距
      borderColor: "rgba(255, 255, 255, 0.6)", // 边框颜色
      borderWidth: 1 // 边框宽度
    },
    backgroundColor: "#fff", // 设置图表背景颜色为白色
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: props.yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.9)", // 背景透明度
      borderColor: "#333", // 边框颜色
      borderWidth: 1,
      textStyle: {
        color: "#fff", // 字体颜色
        fontSize: 12 // 减小字体大小
      },

      // 限制tooltip的最大高度和宽度
      confine: false, // 限制在图表区域内
      enterable: true, // 允许鼠标进入tooltip区域
      hideDelay: 300, // 延迟隐藏，给用户时间移动鼠标到tooltip
      extraCssText:
        "max-height: 300px; overflow-y: auto; max-width: 400px; word-wrap: break-word; pointer-events: auto; box-shadow: 0 4px 12px rgba(0,0,0,0.3); border-radius: 6px;",
      formatter: function (params: any[]) {
        if (!params || params.length === 0) return "";

        // 获取 x 轴的值
        let xValue = params[0].name;
        let tooltipContent = `<div style="font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #666; padding-bottom: 4px;">${xValue}</div>`;

        // 限制显示的系列数量，避免tooltip过长
        const maxDisplayItems = 50;
        const displayParams = params.slice(0, maxDisplayItems);
        const remainingCount = params.length - maxDisplayItems;

        // 按值排序，显示最重要的数据
        const sortedParams = displayParams.sort((a, b) => {
          const aVal = parseFloat(a.value) || 0;
          const bVal = parseFloat(b.value) || 0;
          return aVal - bVal; // 升序排列
        });

        // 展示每个系列的数据
        tooltipContent += sortedParams
          .map((item, index) => {
            let value = "-";
            if (item.value !== null && item.value !== undefined) {
              value = parseFloat(item.value).toFixed(2) + "%";
            }
            // 添加斑马纹背景，便于区分行
            const bgColor =
              index % 2 === 0 ? "rgba(255,255,255,0.05)" : "transparent";
            return `<div style="margin: 1px 0; padding: 4px 6px; display: flex; justify-content: space-between; align-items: center; background-color: ${bgColor}; border-radius: 3px;">
                      <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis;">${item.marker} ${item.seriesName}</span>
                      <span style="font-weight: bold; margin-left: 15px; white-space: nowrap;">${value}</span>
                    </div>`;
          })
          .join("");

        // 如果有更多数据未显示，添加提示
        if (remainingCount > 0) {
          tooltipContent += `<div style="margin-top: 8px; padding-top: 4px; border-top: 1px solid #666; color: #ccc; font-size: 11px;">
                              还有 ${remainingCount} 项数据...
                            </div>`;
        }

        return tooltipContent;
      }
    },
    legend: {
      type: "plain", // 使用普通模式，自动适配
      top: "8%",
      left: "center",
      width: "80%", // 增加宽度利用率
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      data: legendData,
      itemGap: 6, // 减小图例项之间的间距，容纳更多项目
      itemWidth: 12, // 图例标记的宽度
      itemHeight: 12, // 图例标记的高度
      // 自动换行配置
      orient: "horizontal", // 水平排列
      align: "auto", // 自动对齐
      // 当图例项过多时自动换行
      formatter: function (name) {
        // 限制图例文字长度，避免过长
        if (name.length > 15) {
          return name.substring(0, 12) + "...";
        }
        return name;
      }
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    grid: {
      top: `${dynamicGridTop}%`, // 动态调整顶部间距
      left: "3%",
      right: "4%",
      bottom: "18%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xLine
      }
    ],
    yAxis: [
      {
        axisLabel: {
          formatter: function (value) {
            return value + "%";
          }
        },
        type: "value",
        scale: true,
        min: null,
        max: null
      }
    ],
    dataZoom: [
      //滚动条
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 15,
        handleSize: "150%", // 增加手柄的大小
        handleStyle: {
          color: "#fff", // 手柄的颜色
          borderColor: "#5D92F4" // 手柄的边框颜色
        },
        textStyle: {
          color: "#5D92F4" // 滚动条上的文字颜色
        },
        borderColor: "#5D92F4", // 滚动条边框颜色
        fillerColor: "rgba(93, 146, 244, 0.5)", // 选中区域的颜色
        backgroundColor: "rgba(221, 221, 221, 0.2)" // 未选中区域的背景色
      }
    ],
    //visualMap: visualMapPieces,
    series: props.yLine.map((seriesData, index) =>
      typeof seriesData === "object" && seriesData !== null
        ? {
            ...seriesData,
            seriesIndex: index,
            type: "line",
            barWidth: "50%",
            symbolSize: 4, // 如果显示符号，设置较小尺寸
            lineStyle: {
              width: 2 // 线条宽度
            },
            label: {
              show: false,
              position: "top",
              fontSize: 14,
              fontWeight: "bolder"
            },
            color: defaultColors[index % defaultColors.length],
            markLine: {
              symbol: "none",
              data: [
                {
                  yAxis: props.alarmLine,
                  lineStyle: {
                    type: "dashed",
                    color: "red",
                    width: 1
                  }
                }
              ]
            },
            itemStyle: {
              color: params => {
                return props.title.includes("Yield")
                  ? Number(params.value) >= props.alarmLine
                    ? defaultColors[index % defaultColors.length]
                    : "red"
                  : Number(params.value) >= props.alarmLine
                  ? "red"
                  : defaultColors[index % defaultColors.length];
              }
            },
            // 鼠标悬浮时显示数据点
            emphasis: {
              focus: "series",
              lineStyle: {
                width: 3
              }
            }
          }
        : {}
    )
  };
  option && chart.setOption(option);
};

onMounted(() => {
  const chartElement = document.getElementById(props.chartId) as HTMLElement;
  if (chartElement) {
    chart = echarts.init(chartElement);
    initChart();
  }
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine,
    alarmLine: props.alarmLine
  }),
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div :id="chartId" class="chart-container" />
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 这里可以根据需要调整高度 */
}
</style>
