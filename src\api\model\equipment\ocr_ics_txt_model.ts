export interface paramList {
  testTime: String;
  tester: String;
  position: String;
  paramType: String;
  groupId: string;
}

export interface OcrIcsChartVO1 {
  xLine: string[];
  oneLine: number[];
  fiveLine: number[];
  tenLine: number[];
  spec: object;
}

export interface OcrIcsChartVO2 {
  xLine: string[];
  pos1: number[];
  pos2: number[];
  pos3: number[];
  pos4: number[];
  pos5: number[];
  pos6: number[];
  pos7: number[];
  pos8: number[];
  pos9: number[];
  pos10: number[];
  average: number[];
  spec: object;
}

export interface OcrIcsChartVO3 {
  xLine: string[];
  ed1: number[];
  ed2: number[];
  ed3: number[];
  ex1: number[];
  ex2: number[];
  ex3: number[];
  ey1: number[];
  ey2: number[];
  ey3: number[];
}

export interface seriesDataVO {
  name: string;
  type: string;
  stack: string;
  yAxisIndex: number;
  emphasis: {
    focus: "series";
  };
  markPoint: {
    data: [{ type: string; name: string }, { type: string; name: string }];
  };
  markLine: {
    data: [{ type: string; name: string }];
    label: { position: string; formatter: string };
  };
  data: number[];
}

export interface FileVO {
  fileName: string;
  parentName: string;
  minIOUrl: string;
  downloadUrl: string;
}

export interface getFileListParam {
  equipmentId: String;
  date: String;
  time: string;
}

export interface EquipSelectVO {
  id: number;
  equipmentName: string;
  equipmentType: string;
  parentId: number;
  parentName: string;
}

export interface TesterSelectVO {
  id: number;
  testerName: string;
}
