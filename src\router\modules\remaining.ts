import { $t } from "/@/plugins/i18n";
const Layout = () => import("/@/layout/index.vue");

const remainingRouter = [
  {
    path: "/login",
    name: "Login",
    component: () => import("/@/views/login/index.vue"),
    meta: {
      title: $t("menus.hslogin"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      icon: "home-filled",
      title: $t("menus.hshome"),
      showLink: false,
      rank: 104
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("/@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/:pathMatch(.*)",
    name: "404",
    component: () => import("/@/views/error/404.vue"),
    meta: {
      title: $t("menus.hsfourZeroFour"),
      showLink: false
    }
  }
];

export default remainingRouter;
