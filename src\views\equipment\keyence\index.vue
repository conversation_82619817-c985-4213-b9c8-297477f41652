<script setup lang="ts">
import { keyenceAPI } from "/@/api/equipment/keyence";
import { onMounted, reactive } from "vue";
import {
  KeyenceQuery,
  KeyenceVO,
  KeyenceParamVO,
  EquipmentVO
} from "/@/api/model/equipment/keyence_model";
import { Page } from "/@/api/model/domain";
import Line from "./Line.vue";
import { warnMessage } from "/@/utils/message";

defineOptions({
  name: "keyence"
});

const pageData = reactive<{
  componentKey: number;
  loading: boolean;
  selection: any[];
  testList: KeyenceVO[];
  searchTestInfo: KeyenceQuery;
  equipmentList: EquipmentVO[];
  paramList: KeyenceParamVO[];
}>({
  componentKey: 1,
  loading: false,
  selection: [],
  testList: [],
  searchTestInfo: {
    equipmentId: null,
    paramId: null,
    collectDate: "2022-09-16",
    startTime: "08:00:00",
    endTime: "12:00:00",
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  },
  equipmentList: [],
  paramList: []
});

const getPage = () => {
  pageData.loading = true;
  keyenceAPI
    .page(pageData.searchTestInfo)
    .then((res: Page<KeyenceVO[]> | string) => {
      if (res !== "fail") {
        pageData.testList = (res as Page<KeyenceVO[]>).records;
        pageData.searchTestInfo.total = Number(
          (res as Page<KeyenceVO[]>).total
        );
      }
    })
    .finally(() => (pageData.loading = false));
};

const getExport = () => {
  keyenceAPI.exportFile(pageData.searchTestInfo);
};

onMounted(() => {
  pageData.searchTestInfo.collectDate = getTodayDate(new Date());
  getEquipmentSelectList();
});

const getEquipmentSelectList = () => {
  keyenceAPI.getEquipmentSelectList().then((res: EquipmentVO[] | string) => {
    if (res !== "fail") {
      pageData.equipmentList = res as EquipmentVO[];
      pageData.searchTestInfo.equipmentId = pageData.equipmentList[0].id;
      getKeyenceParamByEquipmentId();
    }
  });
};

const getKeyenceParamByEquipmentId = () => {
  let param: { equipmentId: string } = {
    equipmentId: pageData.searchTestInfo.equipmentId
  };
  keyenceAPI
    .getKeyenceParamByEquipmentId(param)
    .then((res: KeyenceParamVO[] | string) => {
      if (res !== "fail") {
        pageData.paramList = res as KeyenceParamVO[];
        pageData.searchTestInfo.paramId = pageData.paramList[0].id;
        pageData.componentKey += 1;
        getPage();
      }
    });
};

const handlerSearch = () => {
  pageData.searchTestInfo.pageNum = 1;
  if (
    pageData.searchTestInfo.paramId != null &&
    pageData.searchTestInfo.paramId != ""
  ) {
    pageData.componentKey += 1;
  }
  let endHour = pageData.searchTestInfo.endTime.split(":")[0];
  let startHour = pageData.searchTestInfo.startTime.split(":")[0];
  if (parseInt(endHour) - parseInt(startHour) > 4) {
    warnMessage("The time frame must be within 4 hours");
    return;
  }
  getPage();
};

const exportSearch = () => {
  pageData.searchTestInfo.pageNum = 1;
  getExport();
};

const handelSelectionChange = val => {
  pageData.selection = val;
};

const sizeChange = (pageSize: number) => {
  pageData.searchTestInfo.pageSize = pageSize;
  getPage();
};

const currentChange = (pageNum: number) => {
  pageData.searchTestInfo.pageNum = pageNum;
  getPage();
};

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const getTodayDate = (date: Date) => {
  let month: string | number = date.getMonth() + 1;
  let strDate: string | number = date.getDate();

  if (month <= 9) {
    month = "0" + month;
  }

  if (strDate <= 9) {
    strDate = "0" + strDate;
  }

  return date.getFullYear() + "-" + month + "-" + strDate;
};

const shortcuts = [
  {
    text: "Today",
    value: new Date()
  },
  {
    text: "Yesterday",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24);
      return date;
    }
  },
  {
    text: "A week ago",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
      return date;
    }
  }
];
</script>

<template>
  <div>
    <el-form :inline="true" :model="pageData.searchTestInfo">
      <el-row>
        <el-col :span="6">
          <el-form-item>
            <el-select
              v-model="pageData.searchTestInfo.equipmentId"
              placeholder="设备"
            >
              <el-option
                v-for="item in pageData.equipmentList"
                :key="item.id"
                :label="item.equipmentName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-select
              v-model="pageData.searchTestInfo.paramId"
              placeholder="参数"
            >
              <el-option
                v-for="item in pageData.paramList"
                :key="item.id"
                :label="item.englishName"
                :value="item.id"
                :title="item.paramName"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item>
            <el-date-picker
              v-model="pageData.searchTestInfo.collectDate"
              type="date"
              placeholder="日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              :shortcuts="shortcuts"
              :clearable="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <el-time-picker
              v-model="pageData.searchTestInfo.startTime"
              value-format="HH:mm:ss"
              placeholder="Arbitrary time"
              :clearable="false"
            />
            <el-time-picker
              v-model="pageData.searchTestInfo.endTime"
              value-format="HH:mm:ss"
              placeholder="Arbitrary time"
              :clearable="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button plain @click="handlerSearch">Query</el-button>
          </el-form-item>
          <el-form-item>
            <el-button plain @click="exportSearch">Export</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-card>
      <template #default>
        <Line :txt="pageData.searchTestInfo" :key="pageData.componentKey" />
      </template>
    </el-card>
    <el-card>
      <el-table
        v-loading="pageData.loading"
        :data="pageData.testList"
        style="width: 100%"
        row-key="id"
        border
        @selection-change="handelSelectionChange"
      >
        <el-table-column
          sortable
          resizable
          :show-overflow-tooltip="true"
          type="selection"
        />
        <el-table-column
          prop="englishName"
          label="参数名称"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="paramName"
          label="备注"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="instruct"
          label="位置"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="originalData"
          label="原数据"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="translateData"
          label="实际数据"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="collectDate"
          label="收集时间"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="continueDate"
          label="持续时间"
          sortable
          resizable
          :show-overflow-tooltip="true"
        />
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchTestInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchTestInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchTestInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped></style>
