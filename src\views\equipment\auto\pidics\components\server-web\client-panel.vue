<script setup lang="ts">
import { onBeforeMount, onMounted, ref } from "vue";
//import adjustRules from "./adjust-rules.vue";
import manualSet from "./manual-set.vue";
import dayjs from "dayjs";

const props = defineProps({
  icsEquipInfo: {
    require: true,
    type: Object,
    default: null
  }
});

const selectedDates = ref();

onBeforeMount(() => {
  selectedDates.value = [dayjs().format("YYYY-MM-DD")];
});

onMounted(() => {});
</script>

<template>
  <el-row class="status-board">
    <el-col :span="24">
      <!-- <el-row>
        <el-col>
          <adjustRules />
        </el-col>
      </el-row> -->
      <el-row>
        <el-col>
          <manualSet :icsEquipInfo="props.icsEquipInfo" />
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>

<style scoped>
.status-board {
  min-width: 860px;
  margin-bottom: 30px;
}

.el-row {
  margin: 0;
  padding: 0;
}
</style>
