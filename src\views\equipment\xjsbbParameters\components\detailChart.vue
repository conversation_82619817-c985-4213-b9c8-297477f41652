<script setup lang="ts">
import { getCurrentInstance, onBeforeMount, onMounted, ref } from "vue";
import * as echarts from "echarts";
import { cloneDeep } from "lodash-unified";
import { XJSBB } from "/@/api/xjsbb/xjsbb";
import dayjs from "dayjs";

const props = defineProps({
  groupId: {
    type: String,
    default: ""
  },
  row: {
    require: true,
    type: Object
  },
  dateRange: {
    type: Array, // 确保类型为日期数组
    required: true
  }
});

// 时间
const selectedDates = ref<string[]>();
let myChart: echarts.ECharts | null = null;

// 表格相关
const tableData = ref<any[]>([]);
const tableColumns = ref<{ prop: string; label: string }[]>([]);

const optionTemplate = {
  graphic: {
    type: "text",
    left: "center",
    top: "middle",
    silent: true,
    invisible: false,
    style: {
      fill: "#9d9d9d",
      fontWeight: "bold",
      text: "No Data",
      fontFamily: "Microsoft YaHei",
      fontSize: 25
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  toolbox: {
    show: true,
    feature: {
      dataZoom: {
        yAxisIndex: "none"
      },
      dataView: { readOnly: false },
      restore: {}
    }
  },
  legend: {
    containerWidth: "auto",
    width: "75%",
    type: "scroll",
    top: "5%",
    textStyle: {
      color: "#4A4A4A",
      fontSize: 10
    },
    borderRadius: 8,
    padding: [8, 10],
    backgroundColor: "rgba(200, 250, 250, 0.1)",
    borderColor: "#ccc",
    borderWidth: 1,
    selector: [
      { type: "all", title: "all" },
      { type: "inverse", title: "inverse" }
    ],
    itemGap: 10
  },
  dataZoom: [
    {
      type: "slider",
      realtime: true,
      xAxisIndex: [0],
      bottom: "10",
      height: 10,
      borderColor: "rgba(0,0,0,0)",
      textStyle: { color: "#05D5FF" }
    }
  ],
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: [],
    axisLabel: {
      interval: "auto",
      rotate: 45,
      formatter: (value: string) => {
        const label = value.substring(
          value.indexOf("-") + 1,
          value.lastIndexOf(".")
        );
        return label.replace("-", "/");
      }
    }
  },
  yAxis: [],
  series: []
};

onBeforeMount(() => {
  selectedDates.value = [dayjs().format("YYYY-MM-DD")];
});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const { proxy } = getCurrentInstance()!;
let rowWidth = ref<number | string>("");

onMounted(() => {
  setTimeout(() => {
    rowWidth.value = proxy.$el.clientWidth;
    if (props.row.variable !== "Alarm") {
      initChart();
    }
    fetchData(selectedDates.value!);
  }, 100);
});

const initChart = () => {
  myChart = echarts.init(
    document.getElementById(props.row.paramId) as HTMLDivElement
  );
};

const fetchData = (selectedDates: string[]) => {
  if (props.row.variable !== "Alarm") {
    myChart?.showLoading();
  }
  XJSBB.getData({
    paramName: props.row.variable,
    selected: selectedDates,
    groupId: props.groupId
  })
    .then((res: any) => {
      if (res !== "fail") {
        setData(res);
      }
    })
    .finally(() => {
      if (props.row.variable !== "Alarm") {
        myChart?.hideLoading();
      }
    });
};

const setData = (data: any) => {
  if (props.row.variable === "Alarm") {
    // 构造表头
    tableColumns.value = [
      { prop: "xAxis", label: "Time" },
      ...Object.keys(data)
        .filter(key => key !== "xAxisData")
        .map(key => ({
          prop: key,
          label: key
        }))
    ];
    // 构造表格数据
    tableData.value = data.xAxisData.map((x: any, idx: number) => {
      const rowObj: any = { xAxis: x };
      Object.keys(data).forEach(key => {
        if (key !== "xAxisData") {
          rowObj[key] = data[key][idx];
        }
      });
      return rowObj;
    });
  } else {
    const hasData = data.xAxisData.length > 0;
    const option = cloneDeep(optionTemplate);
    option.xAxis.data = data.xAxisData;
    option.yAxis = [
      { position: "left", type: "value", scale: true, max: null, min: null }
    ];
    option.series = Object.keys(data)
      .filter(key => key !== "xAxisData")
      .map(key => ({
        name: key,
        type: "line",
        stack: key,
        yAxisIndex: 0,
        zLevel: 0,
        z: 0,
        data: data[key] || []
      }));
    option.graphic.invisible = hasData;
    myChart?.setOption(option);
  }
};

const callReload = (dateRange: string[]) => {
  fetchData(dateRange);
};
</script>

<template>
  <el-row align="middle">
    <el-col align="middle" :span="6">
      <el-date-picker
        v-model="selectedDates"
        value-format="YYYY-MM-DD"
        :disabled-date="disabledDate"
        type="dates"
        placeholder="Pick one or more dates"
        :clearable="false"
        @change="callReload"
      />
    </el-col>
    <el-col align="middle" :span="6" />
  </el-row>
  <el-row align="middle">
    <el-col :span="24">
      <div
        v-if="props.row.variable !== 'Alarm'"
        @click.stop
        :id="props.row.paramId"
        :style="{
          width: rowWidth + 'px',
          height: '300px',
          margin: 0,
          padding: 0
        }"
      />
      <el-table v-else :data="tableData" style="width: 100%">
        <el-table-column
          v-for="col in tableColumns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
        />
      </el-table>
    </el-col>
  </el-row>
</template>

<style scoped>
.chartTitle {
  font-weight: bold;
  font-size: 1em;
}
</style>
