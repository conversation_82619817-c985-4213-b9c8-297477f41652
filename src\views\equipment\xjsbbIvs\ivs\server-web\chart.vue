<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";

// 定义 props 类型
const props = defineProps({
  chartId: {
    type: String,
    required: true
  },
  xLine: {
    type: Array as () => (string | number)[],
    required: true
  },
  yLine: {
    type: Array,
    required: true
  },
  yAxisMin: {
    type: Number,
    default: 0
  },
  yAxisMax: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    required: true
  }
});
let chart: echarts.ECharts | null = null; // 存储ECharts实例;
//let yAxisMin = 0;
let yAxisMax = 0;

//计算 y 轴最小值和最大值
const calculateYAxisRange = () => {
  const allData = props.yLine.flatMap((series: any) => series.data);
  if (allData.length > 0) {
    //yAxisMin = Math.min(...allData);
    yAxisMax = Math.max(...allData);
  } else {
    //yAxisMin = 0;
    yAxisMax = 0;
  }
};

const initChart = () => {
  //清除图表实例
  if (chart) {
    chart.clear();
  }
  if (!chart) return;

  calculateYAxisRange();
  let option: EChartsOption;
  option = {
    title: {
      text: props.title,
      left: "center",
      top: "2%",
      bottom: "2%",
      textStyle: {
        color: "black",
        fontSize: 16,
        fontWeight: "bold",
        fontStyle: "normal",
        fontFamily: "Arial, sans-serif",
        textBorderColor: "rgba(255, 255, 255, 0.6)",
        textBorderWidth: 0,
        shadowColor: "rgba(0, 0, 0, 0.6)"
      },
      backgroundColor: "rgba(245, 245, 245, 0.6)",
      borderRadius: 10,
      padding: [10, 15],
      borderColor: "rgba(255, 255, 255, 0.6)",
      borderWidth: 1
    },
    backgroundColor: "#fff",
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: props.yLine.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "#333",
      borderWidth: 1,
      textStyle: {
        color: "#fff",
        fontSize: 14
      },
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(0,0,0,0.1)"
        }
      }
    },
    // 添加图例
    legend: {
      top: "12%",
      left: "center",
      bottom: "8%",
      width: "80%",
      textStyle: {
        color: "#4A4A4A",
        fontSize: 10
      },
      borderRadius: 8, // 圆角边框
      //padding: [8, 10],
      backgroundColor: "rgba(200, 250, 250, 0.1)", // 渐变背景
      borderColor: "#ccc",
      borderWidth: 1,
      selector: [
        {
          type: "all",
          title: "all" //全选
        },
        {
          type: "inverse",
          title: "inverse" //反选
        }
      ],
      itemGap: 8 // 图例项之间的间距
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    grid: {
      top: "25%", // 增加顶部间距以容纳图例
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: props.xLine || [
          "P1",
          "P2",
          "P3",
          "P4",
          "P5",
          "P6",
          "P7",
          "P8",
          "P9",
          "P10"
        ],
        axisLabel: {
          interval: 0,
          rotate: 0
        }
      }
    ],
    yAxis: [
      {
        type: "value",
        scale: true,
        name: "Count",
        nameTextStyle: {
          fontSize: 14,
          fontWeight: "bold"
        },
        min: 0,
        max: yAxisMax < 10 ? yAxisMax + 10 : null
      }
    ],
    dataZoom: [
      {
        type: "slider",
        realtime: true,
        xAxisIndex: [0],
        bottom: "10",
        height: 15,
        handleSize: "150%",
        handleStyle: {
          color: "#fff",
          borderColor: "#5D92F4"
        },
        textStyle: {
          color: "#5D92F4"
        },
        borderColor: "#5D92F4",
        fillerColor: "rgba(93, 146, 244, 0.5)",
        backgroundColor: "rgba(221, 221, 221, 0.2)"
      }
    ],
    series: props.yLine.map(seriesData =>
      typeof seriesData === "object" && seriesData !== null
        ? {
            ...seriesData,
            type: "bar",
            barWidth: "50%",
            // 移除顶部标签，防止叠加柱状图中标签重叠
            label: {
              show: false
            }
          }
        : {}
    )
  };
  option && chart.setOption(option);
};

onMounted(() => {
  const chartElement = document.getElementById(props.chartId) as HTMLElement;
  if (chartElement) {
    chart = echarts.init(chartElement);
    initChart();
  }
});

watch(
  () => ({
    xLine: props.xLine,
    yLine: props.yLine
  }),
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
  }
});
</script>

<template>
  <div :id="chartId" class="chart-container" />
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px; /* 这里可以根据需要调整高度 */
}
</style>
