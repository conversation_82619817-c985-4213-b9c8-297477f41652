<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElMessageBox } from "element-plus";
import { acrAPIs } from "/@/api/acr/acrAPI";
import { useAcrStoreHook } from "/@/store/modules/acr";
import { reactive } from "vue";

defineOptions({
  name: "AcrControl"
});

const dispatcherEnabled = ref(0);

onMounted(() => {
  acrAPIs
    .getDispatcherSwitch({ areaId: useAcrStoreHook().getAreaId })
    .then((res: any) => {
      dispatcherEnabled.value = parseInt(res.enabled);
    })
    .catch(() => {});
});

const switchDispatcherEnable = () => {
  ElMessageBox.confirm(
    dispatcherEnabled.value
      ? "Ensure all materials are removed from ACR before disable dispatcher!!!"
      : "Ensure all materials are removed from ACR before enable dispatcher!!!",
    "Confirmation",
    {
      confirmButtonText: "OK",
      cancelButtonText: "Cancel",
      type: "warning"
    }
  )
    .then(() => {
      acrAPIs
        .switchDispatcher({ enabled: dispatcherEnabled.value == 1 ? 1 : 0 })
        .then((_res: any) => {
          dispatcherEnabled.value = parseInt(_res.enabled);
        })
        .catch(_err => {
          dispatcherEnabled.value = 1 - dispatcherEnabled.value;
        });
    })
    .catch(() => {
      dispatcherEnabled.value = 1 - dispatcherEnabled.value;
    });
};

const confirmAction = action => {
  ElMessageBox.confirm(`Are you sure you want to ${action}?`, "Confirmation", {
    confirmButtonText: "OK",
    cancelButtonText: "Cancel",
    type: "warning"
  })
    .then(() => {
      console.log(`${action} confirmed`);

      let mapPos = useAcrStoreHook().getMapMarkers.find(
        marker => marker.plantPos == "HOME"
      ).mapPosCode;

      switch (action) {
        case "PAUSE":
          acrAPIs
            .pauseOrder()
            .then((_res: any) => {
              console.log(_res);
              if (_res !== "fail") {
                ElMessageBox.alert("Pause sent.");
              }
            })
            .catch(_err => {
              console.log(_err);
            });
          break;
        case "RESUME":
          acrAPIs
            .resumeOrder()
            .then((_res: any) => {
              console.log(_res);
              if (_res !== "fail") {
                ElMessageBox.alert("Resume sent.");
              }
            })
            .catch(_err => {
              console.log(_err);
            });
          break;
        case "STOP":
          acrAPIs
            .stopOrder()
            .then((_res: any) => {
              console.log(_res);
              if (_res !== "fail") {
                ElMessageBox.alert("Stop sent.");
              }
            })
            .catch(_err => {
              console.log(_err);
            });
          break;
        case "RESET":
          acrAPIs
            .resetOrder()
            .then((_res: any) => {
              if (_res !== "fail") {
                ElMessageBox.alert("Reset sent.");
              }
            })
            .catch(_err => {
              console.log(_err);
            });
          break;
        case "GOHOME":
          acrAPIs
            .moveOrder({ mapPos })
            .then((_res: any) => {
              if (_res !== "fail") {
                ElMessageBox.alert("Order sent.");
              }
            })
            .catch(_err => {
              console.log(_err);
            });
          break;
      }
    })
    .catch(() => {
      console.log(`${action} cancelled`);
      // Handle cancel action here
    });
};

const clearTrayNo = trayNo => {
  ElMessageBox.confirm(
    "Clear stacking " + trayNo.substring(1) + " ?",
    "Confirmation",
    {
      confirmButtonText: "OK",
      cancelButtonText: "Cancel",
      type: "warning"
    }
  )
    .then(() => {
      acrAPIs
        .clearTrayStacking({ stacking: trayNo })
        .then((_res: any) => {
          if (_res !== "fail") {
            ElMessageBox.alert("Stacking " + trayNo.substring(1) + " cleared.");
          }
        })
        .catch(_err => {
          console.log(_err);
        });
    })
    .catch(() => {
      console.log(`cancelled`);
    });
};

const tray = reactive({
  _1: 1,
  _2: 1,
  _3: 1,
  _4: 1,
  _5: 1,
  _6: 1,
  _7: 1,
  _8: 1
});
const confirmTray = () => {
  acrAPIs
    .confirmTray(tray)
    .then((_res: any) => {
      if (_res !== "fail") {
        ElMessageBox.alert("Tray reloaded.");
      }
    })
    .catch(_err => {
      console.log(_err);
    });
};

// const assy = reactive({
//   loadLeftReq: 0,
//   loadRightReq: 0,
//   unloadLeftReq: 0,
//   unloadRightReq: 0,
//   unloadNgReq: 0
// });

// const checkAssy = assyType => {
//   assy[assyType] = 1 - assy[assyType];
// };

// const confirmAssy = () => {
//   acrAPIs
//     .confirmAssy(assy)
//     .then((_res: any) => {
//       if (_res !== "fail") {
//         ElMessageBox.alert("Singal sent.");
//         assy.loadLeftReq = 0;
//         assy.loadRightReq = 0;
//         assy.unloadLeftReq = 0;
//         assy.unloadRightReq = 0;
//         assy.unloadNgReq = 0;
//       }
//     })
//     .catch(_err => {
//       console.log(_err);
//     });
// };
</script>

<template>
  <el-row align="middle">
    <el-col align="center" :span="4">
      <span style="font-weight: bold; font-size: 0.8rem">
        ACR Dispatcher On/Off:
      </span>
      <el-switch
        v-model="dispatcherEnabled"
        @change="switchDispatcherEnable"
        style="
          --el-switch-on-color: #67c23a;
          --el-switch-off-color: #f56c6c;
          margin-left: 10px;
        "
        inline-prompt
        :active-value="1"
        :inactive-value="0"
        active-text="On"
        inactive-text="Off"
      />
    </el-col>
    <el-col align="center" :span="4">
      <el-button type="primary" @click="confirmAction('PAUSE')"
        >PAUSE</el-button
      >
    </el-col>
    <el-col align="center" :span="4">
      <el-button type="primary" @click="confirmAction('RESUME')"
        >RESUME</el-button
      >
    </el-col>
    <el-col align="center" :span="4">
      <el-button type="danger" @click="confirmAction('STOP')">STOP</el-button>
    </el-col>
    <el-col align="center" :span="4">
      <el-button type="danger" @click="confirmAction('RESET')">RESET</el-button>
    </el-col>
    <el-col align="center" :span="4">
      <el-button type="warning" @click="confirmAction('GOHOME')"
        >GO HOME</el-button
      >
    </el-col>
  </el-row>
  <el-divider border-style="dashed" style="margin-bottom: 1rem" />
  <el-row align="middle">
    <!-- <el-col align="center" :span="1"
      ><span style="margin-left: 0.5rem; background-color: #67c23a"
        >Tray:</span
      ></el-col
    > -->
    <el-col align="center" :span="1">
      <el-badge
        :value="
          useAcrStoreHook().getDispatcherStatus.inSensor == 1 ? 'OK' : 'NO TRAY'
        "
        :type="
          useAcrStoreHook().getDispatcherStatus.inSensor == 1
            ? 'success'
            : 'danger'
        "
      >
        <template v-slot:default>
          <div style="white-space: pre-line">
            <span style="font-weight: bold; font-size: 1rem">TRAY:</span>
          </div>
        </template>
      </el-badge>
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._1 == ''"
        @click="clearTrayNo('_1')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._1 == ''
            ? ''
            : 'success'
        "
        >1</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._2 == ''"
        @click="clearTrayNo('_2')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._2 == ''
            ? ''
            : 'success'
        "
        >2</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._3 == ''"
        @click="clearTrayNo('_3')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._3 == ''
            ? ''
            : 'success'
        "
        >3</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._4 == ''"
        @click="clearTrayNo('_4')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._4 == ''
            ? ''
            : 'success'
        "
        >4</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._5 == ''"
        @click="clearTrayNo('_5')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._5 == ''
            ? ''
            : 'success'
        "
        >5</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._6 == ''"
        @click="clearTrayNo('_6')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._6 == ''
            ? ''
            : 'success'
        "
        >6</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._7 == ''"
        @click="clearTrayNo('_7')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._7 == ''
            ? ''
            : 'success'
        "
        >7</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        :disabled="useAcrStoreHook().getDispatcherStatus.trayInfo._8 == ''"
        @click="clearTrayNo('_8')"
        :type="
          useAcrStoreHook().getDispatcherStatus.trayInfo._8 == ''
            ? ''
            : 'success'
        "
        >8</el-button
      >
    </el-col>
    <el-col align="center" :span="2">
      <el-button @click="confirmTray()" type="primary">RELOAD</el-button>
    </el-col>

    <el-col align="center" :span="2">
      <el-badge
        :value="
          useAcrStoreHook().getDispatcherStatus.assyConnect == 1
            ? 'CONNECTED'
            : 'DISCONNECTED'
        "
        :type="
          useAcrStoreHook().getDispatcherStatus.assyConnect == 1
            ? 'success'
            : 'danger'
        "
      >
        <template v-slot:default>
          <div style="white-space: pre-line">
            <span style="font-weight: bold; font-size: 1rem">ASSY:</span>
          </div>
        </template>
      </el-badge>
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.loadingLeft == 1
            ? 'warning'
            : useAcrStoreHook().getDispatcherStatus.loadLeft == 1
            ? 'success'
            : ''
        "
        >L/L</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.loadingRight == 1
            ? 'warning'
            : useAcrStoreHook().getDispatcherStatus.loadRight == 1
            ? 'success'
            : ''
        "
        >L/R</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.unloadingLeft == 1
            ? 'warning'
            : useAcrStoreHook().getDispatcherStatus.unloadLeft == 1
            ? 'success'
            : ''
        "
        >UL/L</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.unloadingRight == 1
            ? 'warning'
            : useAcrStoreHook().getDispatcherStatus.unloadRight == 1
            ? 'success'
            : ''
        "
        >UL/R</el-button
      >
    </el-col>
    <el-col align="center" :span="1">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.unloadingNg == 1
            ? 'warning'
            : useAcrStoreHook().getDispatcherStatus.unloadNg == 1
            ? 'success'
            : ''
        "
        >UL/NG</el-button
      >
    </el-col>

    <el-col align="center" :span="2">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.cspSensor1 == 1 ? 'danger' : ''
        "
        >CSP/OK1</el-button
      >
    </el-col>
    <el-col align="center" :span="2">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.cspSensor2 == 1 ? 'danger' : ''
        "
        >CSP/OK2</el-button
      >
    </el-col>
    <el-col align="center" :span="2">
      <el-button
        size="small"
        disabled
        :type="
          useAcrStoreHook().getDispatcherStatus.cspSensor3 == 1 ? 'danger' : ''
        "
        >CSP/NG</el-button
      >
    </el-col>
    <!-- <el-col align="center" :span="2">
      <el-button @click="confirmAssy()" type="primary">SEND</el-button>
    </el-col> -->
  </el-row>
</template>

<style lang="scss" scoped>
.el-badge {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
}
</style>
