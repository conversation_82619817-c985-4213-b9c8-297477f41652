<script setup lang="ts">
import { onBeforeMount, ref, watch } from "vue";
import { XJSBB } from "/@/api/xjsbb/xjsbb";
import Chart from "./chart.vue"; // Import chart component

// Reactive variables for storing different chart data
const chartDataIVSL = ref<any[]>([]);
const chartDataIVSR = ref<any[]>([]);

const xAxisDataAL = ref<string[]>([]);
const xAxisDataAR = ref<string[]>([]);

const selectButton = ref("current");

interface SearchInfo {
  groupId: string;
  groupName: string;
}

const props = defineProps<{
  groupList: Array<{ groupId: string; groupName: string }>;
  searchInfo: SearchInfo;
}>();

// Store the selected date and time range
const selectedDateRange = ref<string[]>([]);

// Get data function, passing time range as parameters
const fetchData = async (startDateTime: string, endDateTime: string) => {
  XJSBB.getIVSData({
    start_time: startDateTime,
    end_time: endDateTime,
    groupId: props.searchInfo.groupId
  }).then((res: any) => {
    if (res !== "fail") {
      processIVSChartData(res);
    }
  });
};

// 修改的数据处理函数
const processIVSChartData = (res: any) => {
  if (!res) return;

  // 处理左侧IVS数据
  const leftData = res.LeftIVSResult;
  // 处理右侧IVS数据
  const rightData = res.RightIVSResult;
  // 设置X轴数据（p1-p10）
  xAxisDataAL.value = Object.keys(leftData || {}).map(key => key.toUpperCase());
  xAxisDataAR.value = Object.keys(rightData || {}).map(key =>
    key.toUpperCase()
  );

  // 处理左侧IVS数据的叠加柱状图
  chartDataIVSL.value = processStackedBarData(leftData);
  // 处理右侧IVS数据的叠加柱状图
  chartDataIVSR.value = processStackedBarData(rightData);
};

// 新增：处理叠加柱状图数据的函数
const processStackedBarData = (data: any) => {
  if (!data) return [];

  // 获取所有可能的缺陷类型
  const allDefectTypes = new Set<string>();
  Object.values(data).forEach((position: any) => {
    Object.keys(position).forEach(defectType => {
      allDefectTypes.add(defectType);
    });
  });

  // 为每种缺陷类型创建一个数据系列
  const series: any[] = [];
  const positions = Object.keys(data);

  // 颜色映射表，为不同缺陷类型定义颜色
  const colorMap: any = {
    NG: "#F56C6C", // 红色
    T1: "#E6A23C", // 橙色
    T2: "#F2C037", // 黄色
    T3: "#67C23A", // 绿色
    T4: "#409EFF", // 蓝色
    T5: "#8E44AD", // 紫色
    T6: "#16A085", // 青绿色
    T7: "#2C3E50", // 深蓝色
    T8: "#D35400" // 深橙色
  };

  allDefectTypes.forEach(defectType => {
    const seriesData: any = {
      name: defectType,
      type: "bar",
      stack: "defects",
      emphasis: {
        focus: "series"
      },
      data: positions.map(position => {
        // 获取当前位置当前缺陷类型的数量，如果没有则为0
        return parseInt(data[position][defectType] || "0");
      }),
      // 为不同缺陷类型设置不同颜色
      itemStyle: {
        color: colorMap[defectType] || "#91CC75"
      }
    };

    series.push(seriesData);
  });
  return series;
};

// Disable future dates
const disabledDate = (date: Date) => {
  return date > new Date();
};
const getNowDate = (
  type,
  day: number | null = 0,
  toNow: boolean | null = true
) => {
  selectButton.value = type;
  // 获取当前时间
  let now = new Date();

  let shiftStart, shiftEnd;

  if (now.getHours() >= 7 && now.getHours() < 19) {
    // 当前为白班 7:00-19:00
    shiftStart = new Date(now.setHours(7, 0, 0, 0));
    shiftEnd = new Date(now.setHours(19, 0, 0, 0));
  } else {
    // 当前为夜班 19:00-次日 7:00
    shiftStart = new Date(
      now.getHours() < 7 ? now.setDate(now.getDate() - 1) : now
    ).setHours(19, 0, 0, 0);
    shiftEnd = new Date(
      now.getHours() < 7 ? now : now.setDate(now.getDate() + 1)
    ).setHours(7, 0, 0, 0);
  }

  if (day) {
    // 结束日期是否到当前,上个班次
    if (!toNow) {
      shiftStart.setHours(shiftStart.getHours() - day * 24);
      shiftEnd.setHours(shiftEnd.getHours() - day * 24);
    } else {
      shiftStart.setHours(shiftStart.getHours() - day * 24 + 12);
    }
  }
  console.log(shiftStart.toLocaleString(), shiftEnd.toLocaleString());
  selectedDateRange.value = [
    formatDateTime(shiftStart),
    formatDateTime(shiftEnd)
  ];

  fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
};
// 自定义日期格式化函数，确保所有部分都以两位数显示
const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要+1
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

onBeforeMount(() => {
  setTimeout(() => {
    getNowDate(selectButton.value);
  }, 50);
});

watch(
  () => props.searchInfo.groupId,
  () => {
    setTimeout(() => {
      fetchData(selectedDateRange.value[0], selectedDateRange.value[1]);
    }, 50);
  }
);
</script>

<template>
  <div>
    <div>
      <!-- Buttons for Shift and Time Range Selection -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <!-- Time Range Buttons -->
            <el-form-item>
              <el-button
                class="custom-button"
                :class="{ active: selectButton === 'current' }"
                @click="getNowDate('current')"
                >Current Shift</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === 'last' }"
                @click="getNowDate('last', 0.5, false)"
                >Last Shift</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === 'daily' }"
                @click="getNowDate('daily', 1)"
                >Daily</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === 'weekly' }"
                @click="getNowDate('weekly', 7)"
                >Weekly</el-button
              >
              <el-button
                class="custom-button"
                :class="{ active: selectButton === 'monthly' }"
                @click="getNowDate('monthly', 30)"
                >Monthly</el-button
              >
            </el-form-item>
            <!-- Date Picker -->
            <el-form-item>
              <el-date-picker
                v-model="selectedDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                @change="value => fetchData(value[0], value[1])"
                class="date-picker"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- Render four charts -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-al"
            :xLine="xAxisDataAL"
            :yLine="chartDataIVSL"
            title="L-IVS defect distribution by tray position"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar"
            :xLine="xAxisDataAR"
            :yLine="chartDataIVSR"
            title="R-IVS defect distribution by tray position"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.chart-row {
  margin-bottom: 20px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.custom-button {
  border-radius: 10px;
  background-color: white;
  color: #333;
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.custom-button.active {
  background-color: #208efc;
  color: white;
  border: 1px solid #dcdfe6;
}

.date-picker {
  width: 400px;
  margin-left: 20px;
}
</style>
