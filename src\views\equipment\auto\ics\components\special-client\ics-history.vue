<script setup lang="ts">
import { icsAutoAdjustAPI } from "/@/api/local/icsAutoAdjust";
import { onMounted, onBeforeUnmount, ref } from "vue";
import dayjs from "dayjs";

const props = defineProps({
  selectedDates: {
    require: true,
    type: Array,
    default: null
  }
});

const scrollbarRef = ref(null);

const history = ref([]);

let timer = null;

onMounted(() => {
  initHistory(props.selectedDates);
});

const clearTimer = () => {
  clearInterval(timer);
  timer = null;
};

onBeforeUnmount(() => {
  clearTimer();
});

const initHistory = selectedDates => {
  if (selectedDates.length > 1) {
    selectedDates = selectedDates.join(",");
  }
  icsAutoAdjustAPI
    .fetchHistory({ selected: selectedDates })
    .then((res: any) => {
      if (res !== "fail") {
        history.value = res;
        if (res.length == 0) {
          history.value.push({
            content: "Empty",
            timestamp: "Selected Dates"
          });
        }
        if (timer !== null) {
          clearTimer();
        }
        timer = setInterval(() => {
          let lastHistory = history.value[history.value.length - 1];
          if (lastHistory !== undefined) {
            appendHistory(lastHistory.timestamp);
          }
        }, 5000);
      }
    })
    .catch(_err => {
      console.log(_err);
    });
};

const appendHistory = lastTime => {
  if (lastTime === "Selected Dates") {
    lastTime = dayjs().format("YYYY-MM-DD HH:mm:ss.SSS");
  }
  icsAutoAdjustAPI
    .fetchLastestHistory({ lastTime })
    .then((res: any) => {
      if (res !== "fail" && res.length > 0) {
        history.value.push(...res);
        setTimeout(() => {
          scrollToBottom();
        }, 50);
      }
    })
    .catch(_err => {
      console.log(_err);
    });
};

const reloadHistory = selectedDates => {
  console.log(selectedDates);
  initHistory(selectedDates);
};

const scrollToBottom = () => {
  let timelineDom = document.getElementById("historyTimeline");
  scrollbarRef.value.setScrollTop(timelineDom.clientHeight);
};

defineExpose({
  reloadHistory
});
</script>

<template>
  <el-scrollbar
    ref="scrollbarRef"
    style="
      margin-top: 10px;
      margin-left: 5px;
      padding: 10px;
      min-height: 33em;
      max-height: 33em;
    "
    class="event-border"
  >
    <el-timeline id="historyTimeline">
      <el-timeline-item
        v-for="(activity, index) in history"
        :key="index"
        :timestamp="activity.timestamp"
        placement="top"
      >
        <el-card>
          {{ activity.content }}
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </el-scrollbar>
</template>

<style scoped>
.event-border {
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-small);
  box-shadow: var(--el-box-shadow);
}
</style>
