import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/equipment/xjsbb",
  GROUP = "/group",
  LIST = "/list/page",
  DATA = "/data",
  IVS_DATA = "/ivs/data",
  IVSDATA = "/ivsData",
  TRAY = "/tray"
}

class XJSBBAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getList<T>(): Promise<T> {
    return this.get<T>(API.GROUP);
  }

  //带参post
  getData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.DATA, query);
  }

  //分页查询
  getPage<T, Q>(query: Q): Promise<T> {
    return this.post<T>(API.LIST, query);
  }

  getIVSData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.IVS_DATA, query);
  }

  IVSData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.IVSDATA, query);
  }

  getTray<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.TRAY, query);
  }
}

export const XJSBB = new XJSBBAPI();
