#!/bin/bash
# 脚本需在有podman环境运行
# 定义通用信息
USERNAME="podman"
REMOTE_PATH="/data/podman/packages"
BAK_PATH="/data/podman/packages/history"
PPK_FILE="podman.pem"
LOCAL_DIR="../dist"
IMAGE_NAME="hds_ui:0.0.1"
TAR_FILE="hds_ui.tar"
DIR="dist"

# 定义服务器地址
SERVER1="*************"

# 保存当前目录
SCRIPT_DIR="$(dirname "$(realpath "$0")")"

# 在当前目录的上级目录执行 npm run build
echo "Running npm build in the parent directory..."
cd "$SCRIPT_DIR/.." || exit
npm run build

# 返回到脚本所在的目录
cd "$SCRIPT_DIR" || exit

# 确保 dist 目录已生成
if [ ! -d "$LOCAL_DIR" ]; then
    echo "Error: dist directory not found!"
    exit 1
fi

# 将 dist 目录复制到当前目录（保留原目录不变）
echo "Copying dist directory to the current directory..."
cp -r "$LOCAL_DIR" "$(dirname "$0")"

# 构建 Podman 镜像
echo "Building Podman image $IMAGE_NAME..."
podman build -t $IMAGE_NAME .

# 确保镜像构建成功
if [ $? -ne 0 ]; then
    echo "Error: Failed to build Podman image!"
    exit 1
fi

# 保存镜像为 tar 文件
echo "Saving Podman image as $TAR_FILE..."
podman save -o $TAR_FILE $IMAGE_NAME

# 确保镜像保存成功
if [ $? -ne 0 ]; then
    echo "Error: Failed to save Podman image as tar file!"
    exit 1
fi

# 备份和发送文件的函数
backup_and_send() {
    local server=$1
    echo "Checking for existing tar file on $server..."

    # 检查服务器上是否存在同名 tar 文件
    if ssh -i $PPK_FILE $USERNAME@$server "[ -f $REMOTE_PATH/$TAR_FILE ]"; then
        echo "Tar file exists on $server. Backing up..."
        TIMESTAMP=$(date +%Y%m%d%H%M%S)
        ssh -i $PPK_FILE $USERNAME@$server "mv $REMOTE_PATH/$TAR_FILE $BAK_PATH/${TAR_FILE%.*}_backup_$TIMESTAMP.${TAR_FILE##*.}"
    else
        echo "No existing tar file found on $server."
    fi

    # 发送 tar 文件到服务器
    echo "Sending tar file to $server..."
    scp -i $PPK_FILE $TAR_FILE $USERNAME@$server:$REMOTE_PATH

    # 执行远程脚本
    echo "Executing deploy_ui.sh on $server..."
    ssh -i $PPK_FILE $USERNAME@$server "bash $REMOTE_PATH/deploy_ui.sh"
}

# 处理服务器
backup_and_send $SERVER1
# 删除dist目录
echo "Deleting dist directory..."
rm -rf "$DIR"

echo "File transfer complete."
