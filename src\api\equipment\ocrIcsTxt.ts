import { http } from "/@/utils/http";
import BaseRequest from "../base";
enum API {
  BASE_URL = "/ocr/ics"
}

class OcrIcsTxtAPI extends BaseRequest {
  private getChartUrl1: string;
  private getChartUrl2: string;
  private getChartUrl3: string;
  private getTesterListUrl: string;
  private getGroupListUrl: string;

  constructor() {
    super();
    this.getChartUrl1 = "/getChartData1";
    this.getChartUrl2 = "/getChartData2";
    this.getChartUrl3 = "/getChartData3";
    this.getTesterListUrl = "/getTesterSelectList";
    this.getGroupListUrl = "/getGroupSelectList";
  }

  getBaseUrl(): string {
    return API.BASE_URL;
  }

  ParamType: Array<String> = [
    "ED1",
    "ED2",
    "EY1",
    "EY2",
    "EX1",
    "EX2",
    "ED3",
    "EY3",
    "EX3"
  ];

  posList: Array<{ name: string; value: string }> = [
    { name: "P1", value: "1" },
    { name: "P2", value: "2" },
    { name: "P3", value: "3" },
    { name: "P4", value: "4" },
    { name: "P5", value: "5" },
    { name: "P6", value: "6" },
    { name: "P7", value: "7" },
    { name: "P8", value: "8" },
    { name: "P9", value: "9" },
    { name: "P10", value: "10" }
  ];

  getChartData1<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getChartUrl1, query);
  }

  getChartData1s(params): any {
    return http.request(
      "post",
      "http://10.10.200.172/api/ocr/ics/getChartData1",
      {
        data: params
      }
    );
  }

  getChartData2<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getChartUrl2, query);
  }

  getChartData2s(params): any {
    return http.request(
      "post",
      "http://10.10.200.172/api/ocr/ics/getChartData2",
      {
        data: params
      }
    );
  }

  getChartData3<T, Q>(query: Q): Promise<T> {
    return this.post<T>(this.getChartUrl3, query);
  }

  getChartData3s(params): any {
    return http.request(
      "post",
      "http://10.10.200.172/api/ocr/ics/getChartData3",
      {
        data: params
      }
    );
  }

  getTesterSelectList<T>(param: { groupId: String }): Promise<T> {
    return this.get<T>(this.getTesterListUrl, param);
  }

  getTesterSelectLists(params): any {
    return http.request(
      "get",
      "http://10.10.200.172/api/ocr/ics/getTesterSelectList",
      {
        params
      }
    );
  }

  getGroupSelectList<T>(param: { testTime: String }): Promise<T> {
    return this.get<T>(this.getGroupListUrl, param);
  }

  getGroupSelectLists(params): any {
    return http.request(
      "get",
      "http://10.10.200.172/api/ocr/ics/getGroupSelectList",
      {
        params
      }
    );
  }
}

export const ocrIcsTxtAPI = new OcrIcsTxtAPI();
