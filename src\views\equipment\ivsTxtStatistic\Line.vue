<script setup lang="ts">
import { onMounted, reactive } from "vue";
import * as echarts from "echarts";
import {
  seriesDataVO,
  ivsTxtVO
} from "/@/api/model/equipment/ivs_txt_statistic";

type EChartsOption = echarts.EChartsOption;

const init = (seriesData: Array<Object>) => {
  let myChart = echarts.init(document.getElementById("main"));
  let option: EChartsOption;

  option = {
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: seriesData.length > 0,
      style: {
        fill: "#9d9d9d",
        fontWeight: "bold",
        text: "No Data",
        fontFamily: "Microsoft YaHei",
        fontSize: 25
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {},
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          yAxisIndex: "none"
        },
        dataView: { readOnly: false },
        magicType: { type: ["line", "bar"] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: pageData.xAxisData
      }
    ],
    yAxis: [
      {
        type: "value"
      }
    ],
    series: seriesData
  };
  option && myChart.setOption(option);
};

const pageData = reactive<{
  xAxisData: string[];
  seriesData: Array<Object>;
}>({
  xAxisData: ["P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "P9", "P10"],
  seriesData: []
});

defineOptions({
  name: "ivsTxtStatisticLine"
});

const props = defineProps({
  txt: {
    type: Array<ivsTxtVO>,
    required: true
  }
});

onMounted(() => {
  pageData.seriesData = [];
  for (var i = 0; i < props.txt.length; i++) {
    var ivo = props.txt[i];
    var svo: seriesDataVO = {
      name: ivo.result,
      type: "bar",
      stack: "t",
      emphasis: {
        focus: "series"
      },
      data: [
        ivo.result0,
        ivo.result1,
        ivo.result2,
        ivo.result3,
        ivo.result4,
        ivo.result5,
        ivo.result6,
        ivo.result7,
        ivo.result8,
        ivo.result9
      ]
    };
    pageData.seriesData.push(svo);
  }
  init(pageData.seriesData);
});
</script>

<template>
  <div id="main" style="width: 100%; height: 400px" />
</template>

<style scoped></style>
