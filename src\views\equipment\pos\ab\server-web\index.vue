<script setup lang="ts">
import { onBeforeMount, reactive, ref } from "vue";
import { abPosAPIs } from "/@/api/ab/abPos";
import { abAutoAdjustAPIs } from "/@/api/ab/abAutoAdjust";
import Chart from "./chart.vue"; // 导入 chart 组件
import dayjs from "dayjs";

// 创建四个响应式变量，用来存储不同表格的数据
const chartDataAL = ref<any[]>([]);
const chartDataAR = ref<any[]>([]);
const chartDataBL = ref<any[]>([]);
const chartDataBR = ref<any[]>([]);

const xAxisDataAL = ref<string[]>([]);
const xAxisDataAR = ref<string[]>([]);
const xAxisDataBL = ref<string[]>([]);
const xAxisDataBR = ref<string[]>([]);

// 用于存储选中的日期
const selectedDate = ref<string>("");
const pageData = reactive({
  groupList: [],
  searchInfo: {
    groupId: ""
  }
});

// 获取数据的函数，传递时间参数
const fetchData = async (date: string) => {
  // abAutoAdjustAPIs.getGroupList().then((res: any) => {
  //   pageData.groupList = res;
  //   pageData.searchInfo.groupId = pageData.groupList[0].groupId;
  abPosAPIs
    .getAbPosData({ selected: date, groupId: pageData.searchInfo.groupId })
    .then((res: any) => {
      if (res !== "fail") {
        processChartData(res);
      }
    });
  //});
};

// 数据处理函数，将不同的数据分配给对应的表格
const processChartData = (res: any) => {
  console.log(res);
  // 更新 chartData
  xAxisDataAL.value = res["AB-L-A"].xAxis;
  xAxisDataAR.value = res["AB-R-A"].xAxis;
  xAxisDataBL.value = res["AB-L-B"].xAxis;
  xAxisDataBR.value = res["AB-R-B"].xAxis;
  chartDataAL.value = getSeriesFromResData("AB-L-A", res["AB-L-A"]);
  chartDataAR.value = getSeriesFromResData("AB-R-A", res["AB-R-A"]);
  chartDataBL.value = getSeriesFromResData("AB-L-B", res["AB-L-B"]);
  chartDataBR.value = getSeriesFromResData("AB-R-B", res["AB-R-B"]);
};

// 辅助函数，将数据转换为图表需要的格式
const getSeriesFromResData = (chartName: string, series: any) => {
  const seriesData: any[] = [];

  // 遍历 p0-p9 数据
  for (let i = 0; i <= 9; i++) {
    const pName = `p${i + 1}`;
    const pKey = `p${i}`;
    if (series[pKey]) {
      seriesData.push({
        name: `${pName}`,
        type: "line",
        stack: `${pName}`,
        yAxisIndex: 0,
        emphasis: {
          focus: "series"
        },
        markPoint: null,
        markLine: null,
        data: series[pKey] || [] // 数据为 p0-p9 的值
      });
    }
  }

  return seriesData;
};

// 日期选择限制，禁用今天以后的日期
const disabledDate = (date: Date) => {
  return date > new Date();
};

// 监听日期选择变化事件
const onDateChange = (date: string) => {
  selectedDate.value = date;
  fetchData(date); // 当选择日期时，重新获取数据
};

onBeforeMount(() => {
  const today = dayjs().format("YYYY-MM-DD"); // 获取今天的日期
  selectedDate.value = today;
  //fetchData(today); // 页面加载时默认获取当天的数据
  abAutoAdjustAPIs.getGroupList().then((res: any) => {
    pageData.groupList = res;
    pageData.searchInfo.groupId = pageData.groupList[0].groupId;
    abPosAPIs
      .getAbPosData({ selected: today, groupId: pageData.searchInfo.groupId })
      .then((res: any) => {
        if (res !== "fail") {
          processChartData(res);
        }
      });
  });
});

const reloadByGroup = () => {
  abPosAPIs
    .getAbPosData({
      selected: selectedDate.value,
      groupId: pageData.searchInfo.groupId
    })
    .then((res: any) => {
      if (res !== "fail") {
        processChartData(res);
      }
    });
};
</script>

<template>
  <div>
    <div>
      <el-card class="query-card">
        <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
          <el-form-item label="Line ID">
            <el-select
              v-model="pageData.searchInfo.groupId"
              placeholder="Line ID"
              @change="reloadByGroup"
            >
              <el-option
                v-for="item in pageData.groupList"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>
      <!-- 添加时间筛选器，限制选择日期不超过今天 -->
      <el-row :gutter="20" class="filter-row">
        <el-col :span="24">
          <el-form :inline="true" label-width="80px" class="filter-form">
            <el-form-item label="选择日期">
              <el-date-picker
                v-model="selectedDate"
                type="date"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                @change="onDateChange"
                class="date-picker"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 渲染四个表格，每个表格占据整行 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-al"
            :xLine="xAxisDataAL"
            :yLine="chartDataAL"
            title="L-A"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-bl"
            :xLine="xAxisDataBL"
            :yLine="chartDataBL"
            title="L-B"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-ar"
            :xLine="xAxisDataAR"
            :yLine="chartDataAR"
            title="R-A"
          />
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <chart
            chartId="chart-br"
            :xLine="xAxisDataBR"
            :yLine="chartDataBR"
            title="R-B"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.chart-row {
  margin-bottom: 80px;
}

.filter-row {
  margin-bottom: 20px;
}

.filter-form {
  /* 上间距: 20px; */
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.date-picker {
  width: 200px;
}
</style>
