<script setup lang="ts">
import { onBeforeMount, ref, reactive } from "vue";
import { icsAutoAdjustAPI } from "/@/api/local/icsAutoAdjust";
import { icsAutoAdjustAPIs } from "/@/api/ics/icsAutoAdjust";
import localView from "./components/local-client/index.vue";
import specialView from "./components/special-client/index.vue";
import remoteView from "./components/server-web/index.vue";
import specialWebView from "./components/special-web/index.vue";
import { useICSAutoStoreHook } from "/@/store/modules/icsAuto";

defineOptions({
  name: "autoIcs"
});

// 0 初始化, 1 Client, 2 Server, 3 Special
const isLocal = ref(0);

// 某些特殊机型
const SpecialEquipName = ["GPM2", "J54", "CCM6"];

const componentKey = ref(0);
const flag = ref(true);
const icsEquipInfo = reactive({
  ics_l: null,
  ics_r: null
});

const pageData = reactive({
  groupList: [],
  searchInfo: {
    groupId: "",
    groupName: ""
  }
});

onBeforeMount(() => {
  icsAutoAdjustAPI
    .getICSInfo()
    .then((res: any) => {
      if (res !== "fail") {
        icsEquipInfo.ics_l = res.ics_l;
        icsEquipInfo.ics_r = res.ics_r;
        //判断res.ics_l.clientCode是否在SpecialEquipName中，模糊匹配
        if (
          res.ics_l &&
          SpecialEquipName.some(item => res.ics_l.clientCode.includes(item))
        ) {
          isLocal.value = 3;
        } else {
          isLocal.value = 1;
        }
      } else {
        isLocal.value = 1;
      }
    })
    .catch(_err => {
      icsAutoAdjustAPIs.getGroupList().then((res: any) => {
        pageData.groupList = res;
        pageData.searchInfo.groupId = pageData.groupList[0].groupId;
        pageData.searchInfo.groupName = pageData.groupList[0].groupName;
        getICS(pageData.searchInfo.groupId);
        if (SpecialEquipName.some(item => res[0].groupName.includes(item))) {
          isLocal.value = 4;
        } else {
          isLocal.value = 2;
        }
      });
    });
});

const getICS = groupId => {
  useICSAutoStoreHook().setGroupId(groupId);
  icsAutoAdjustAPIs
    .getICSInfo({ groupId })
    .then((res: any) => {
      if (res !== "fail") {
        icsEquipInfo.ics_l = res.ics_l;
        icsEquipInfo.ics_r = res.ics_r;
        forceRerender();
      }
    })
    .catch(_err => {
      console.log(_err);
    });
  //isLocal.value = 2;
};

const reloadByGroup = () => {
  useICSAutoStoreHook().setGroupId(pageData.searchInfo.groupId);
  flag.value = false;
  //根据groupId从groupList获取groupName信息
  pageData.searchInfo.groupName = pageData.groupList.find(
    item => item.groupId === pageData.searchInfo.groupId
  ).groupName;
  if (
    SpecialEquipName.some(item => pageData.searchInfo.groupName.includes(item))
  ) {
    //等待2s
    setTimeout(() => {
      isLocal.value = 4;
      flag.value = true;
    }, 500);
  } else {
    setTimeout(() => {
      isLocal.value = 2;
      flag.value = true;
    }, 200);
  }
  console.log("pageData", pageData);
  getICS(pageData.searchInfo.groupId);
};

const forceRerender = () => {
  componentKey.value += 1;
};
</script>

<template>
  <div>
    <el-card v-if="isLocal === 2 || isLocal === 4" class="query-card">
      <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
        <el-form-item label="Line ID">
          <el-select
            v-model="pageData.searchInfo.groupId"
            placeholder="Line ID"
            @change="reloadByGroup"
          >
            <el-option
              v-for="item in pageData.groupList"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <el-row align="middle">
        <el-col :span="24" align="left">
          <span style="font-weight: bold; font-size: 20px">
            ICS PID Adjust
          </span>
        </el-col>
      </el-row>
      <el-divider />
      <localView
        v-if="isLocal === 1 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)"
        :icsEquipInfo="icsEquipInfo"
      />
      <remoteView
        :key="componentKey"
        v-else-if="
          flag && isLocal === 2 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)
        "
        :icsEquipInfo="icsEquipInfo"
      />
      <specialView
        v-else-if="isLocal === 3 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)"
        :icsEquipInfo="icsEquipInfo"
      />
      <specialWebView
        v-else-if="
          flag && isLocal === 4 && (icsEquipInfo.ics_l || icsEquipInfo.ics_r)
        "
        :icsEquipInfo="icsEquipInfo"
      />

      <el-row v-else-if="isLocal === 0" align="middle">
        <el-col :span="24" align="middle">
          <span style="font-weight: bold; font-size: 20px">
            Initializing...
          </span>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<style scoped>
.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}
</style>
