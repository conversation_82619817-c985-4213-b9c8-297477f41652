<template>
  <el-drawer
    v-model="drawer"
    direction="rtl"
    :visible="props.addIndexPage"
    @close="props.closeCallBack"
    size="50%"
  >
    <template #header>
      <h4>add equipment</h4>
    </template>
    <template #default>
      <div>
        <el-form
          ref="formData"
          :inline="true"
          :model="pageData.SaveParam"
          label-width="150px"
          :rules="rules"
          status-icon
        >
          <el-form-item
            label="设备名称"
            prop="equipmentName"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.equipmentName"
              placeholder="设备名称"
            />
          </el-form-item>
          <el-form-item
            label="设备编号"
            prop="equipmentCode"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.equipmentCode"
              placeholder="设备编号"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="设备类型"
            prop="equipmentType"
            style="width: 80%"
          >
            <el-select
              v-model="pageData.SaveParam.equipmentType"
              placeholder="设备类型"
            >
              <el-option
                v-for="item in userEquipmentAPI.EquipmentType"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="业务地址" prop="address" style="width: 80%">
            <el-input
              v-model="pageData.SaveParam.address"
              placeholder="业务地址"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="生产线"
            v-if="pageData.SaveParam.equipmentType == '1'"
            prop="jarId"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.jarId"
              placeholder="生产线"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="生产线"
            v-if="
              pageData.SaveParam.equipmentType != '1' &&
              pageData.SaveParam.equipmentType != '6'
            "
            prop="parentId"
            style="width: 80%"
          >
            <el-select
              v-model="pageData.SaveParam.parentId"
              placeholder="生产线"
            >
              <el-option
                v-for="item in pageData.JarDetailList"
                :key="item.id"
                :label="item.jarId"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="账号分组"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserDomain"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.fileUserDomain"
              placeholder="账号分组"
              clearable
            />
          </el-form-item>

          <el-form-item
            label="账号"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserAccount"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.fileUserAccount"
              placeholder="账号"
              clearable
            />
          </el-form-item>

          <el-form-item
            label="密码"
            v-if="pageData.SaveParam.equipmentType == '6'"
            prop="fileUserPws"
            style="width: 80%"
          >
            <el-input
              v-model="pageData.SaveParam.fileUserPws"
              placeholder="密码"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">cancel</el-button>
        <el-button type="primary" @click="confirmClick(formData)"
          >confirm</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { ElMessageBox, FormRules, FormInstance } from "element-plus";
import { userEquipmentAPI } from "/@/api/equipment/userEquipment";
import {
  EquipmentSaveParam,
  EquipmentJarId
} from "/@/api/model/equipment/user_equipment_model";

const drawer = ref(true);

onMounted(() => {
  getJarIdList();
});

const formData = ref<FormInstance>();

const pageData = reactive<{
  JarDetailList: EquipmentJarId[];
  SaveParam: EquipmentSaveParam;
}>({
  JarDetailList: [],
  SaveParam: {
    id: null,
    equipmentName: null,
    jarId: null,
    equipmentType: "1",
    equipmentCode: null,
    address: null,
    parentId: null,
    fileUserDomain: null,
    fileUserAccount: null,
    fileUserPws: null
  }
});

const getJarIdList = () => {
  userEquipmentAPI.getJarIdList().then((res: EquipmentJarId[] | string) => {
    if (res !== "fail") {
      pageData.JarDetailList = res as EquipmentJarId[];
    }
  });
};

function cancelClick() {
  drawer.value = false;
}

const confirmClick = async (fromData: FormInstance | undefined) => {
  if (!fromData) return;
  fromData.validate(valid => {
    if (valid) {
      ElMessageBox.confirm("Are you confirm to close?")
        .then(() => {
          userEquipmentAPI
            .save(pageData.SaveParam)
            .then((res: string) => {
              ElMessageBox.alert(res || "新增成功");
              drawer.value = false;
            })
            .catch()
            .finally();
        })
        .catch(() => {
          // catch error
        });
    } else {
      ElMessageBox.alert("The parameter is incomplete");
    }
  });
};

const props = defineProps({
  addIndexPage: {
    type: Boolean,
    default: false
  },

  closeCallBack: {
    type: Function
  }
});

const rules = reactive<FormRules>({
  equipmentName: [
    {
      required: true,
      message: "Please input Activity equipmentName",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  equipmentCode: [
    {
      required: true,
      message: "Please input Activity equipmentCode",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  equipmentType: [
    {
      required: true,
      message: "Please input Activity equipmentType",
      trigger: "blur"
    }
  ],
  parentId: [
    {
      required: true,
      message: "Please input Activity jarId",
      trigger: "blur"
    }
  ],
  jarId: [
    {
      required: true,
      message: "Please input Activity jarId",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  address: [
    {
      required: true,
      message: "Please input Activity address",
      trigger: "blur"
    },
    { min: 8, max: 40, message: "Length should be 8 to 40", trigger: "blur" }
  ],
  fileUserDomain: [
    {
      required: true,
      message: "Please input Activity fileUserDomain",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  fileUserAccount: [
    {
      required: true,
      message: "Please input Activity fileUserAccount",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ],
  fileUserPws: [
    {
      required: true,
      message: "Please input Activity fileUserPws",
      trigger: "blur"
    },
    { min: 2, max: 20, message: "Length should be 2 to 20", trigger: "blur" }
  ]
});
</script>
