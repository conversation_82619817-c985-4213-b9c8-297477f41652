import { http } from "/@/utils/http";

class IVSPosAPI {
  getIVSInfo(): any {
    return http.request("get", "http://localhost:8080/client/ivs/info", null);
  }

  getIVSData(params): any {
    return http.request("get", "http://localhost:8080/client/ivs/ivsData", {
      params
    });
  }

  getPadData(params): any {
    return http.request("get", "http://localhost:8080/client/ivs/padData", {
      params
    });
  }

  getAllData(params): any {
    return http.request("get", "http://localhost:8080/client/ivs/allData", {
      params
    });
  }

  getIVSAlarmPercent(): any {
    return http.request(
      "get",
      "http://localhost:8080/client/ivs/getIVSAlarmPercent"
    );
  }
}

export const iVSPosAPI = new IVSPosAPI();
