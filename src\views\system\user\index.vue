<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { ElMessageBox } from "element-plus";
import { transformI18n } from "/@/plugins/i18n";

import { userAPI } from "/@/api/system/user";
import { roleAPI } from "/@/api/system/role";

defineOptions({
  name: "User Manage"
});

const pageData = reactive({
  loading: false,
  selection: [],
  dataList: [],
  searchInfo: {
    searchText: "",
    sortColumn: [],
    groupColumn: [],
    pageSize: 10,
    pageNum: 1,
    total: 0
  }
});

const selectedRole = ref();
const selectedUser = ref();

const roleList = ref([]);

const getPage = () => {
  pageData.loading = true;
  pageData.dataList = [];
  userAPI
    .page(pageData.searchInfo)
    .then((res: any) => {
      if (res !== "fail") {
        res.records.map((item, _index) => {
          pageData.dataList.push(
            Object.assign(item, { alterRoleVisible: false })
          );
        });
        pageData.searchInfo.total = Number(res.total);
      }
    })
    .finally(() => (pageData.loading = false));
};

onMounted(() => {
  getPage();
});

const onSearch = () => {
  getPage();
};

const resetSearch = () => {
  pageData.searchInfo.searchText = "";
};

const sizeChange = pageSize => {
  pageData.searchInfo.pageSize = pageSize;
  getPage();
};

const currentChange = pageNum => {
  pageData.searchInfo.pageNum = pageNum;
  getPage();
};

const handleAssignRole = row => {
  row.alterRoleVisible = true;

  let searchInfo = {
    roleName: "",
    role: "",
    type: -1,
    sortColumn: [],
    groupColumn: [],
    pageSize: 65535,
    pageNum: 1,
    total: 0
  };
  roleAPI.page(searchInfo).then((res: any) => {
    if (res !== "fail") {
      roleList.value = res.records;
      selectedUser.value = row.id;
    }
  });
};

const handleDisableUser = row => {
  ElMessageBox({
    title: transformI18n("dialogue.title-warning"),
    message: transformI18n("dialogue.msg-confirm-disable"),
    confirmButtonText: transformI18n("dialogue.confirm"),
    cancelButtonText: transformI18n("dialogue.cancel"),
    showCancelButton: true
  })
    .then(() => {
      userAPI.toggleUser(row.id).then((res: any) => {
        if (res !== "fail") {
          getPage();
        }
      });
    })
    .catch(_err => {});
};

const rowState = ({ row }) => {
  if (row.isEnabled === "0") {
    return {
      backgroundColor: "#e5adb2"
    };
  }
};

const onAssignRoleConfirm = row => {
  userAPI
    .setRole(selectedUser.value, selectedRole.value)
    .then((res: any) => {
      if (res !== "fail") {
        ElMessageBox.alert("Role changed.");
        getPage();
      }
    })
    .catch(err => {
      ElMessageBox.alert(err);
    })
    .finally(() => {
      row.alterRoleVisible = false;
    });
};
</script>

<template>
  <div>
    <el-card class="query-card">
      <el-form :inline="true" :model="pageData.searchInfo" @submit.prevent>
        <el-form-item :label="transformI18n('user.list-search-user')">
          <el-input
            v-model="pageData.searchInfo.searchText"
            placeholder="Staff Number / Name(English/Chinese) / Extension / Department"
            clearable
            class="!w-[500px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            :loading="pageData.loading"
            @click="onSearch"
          >
            {{ transformI18n("query.search") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('refresh')"
            :loading="pageData.loading"
            @click="resetSearch"
          >
            {{ transformI18n("query.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <el-row align="middle">
        <el-col :span="24" align="left">
          <span style="font-weight: bold; font-size: 20px">{{
            transformI18n("user.list-title")
          }}</span>
        </el-col>
      </el-row>
      <el-divider />
      <el-table
        v-loading="pageData.loading"
        :data="pageData.dataList"
        style="width: 100%"
        row-key="id"
        :row-style="rowState"
        border
      >
        <el-table-column
          prop="deptName"
          :label="transformI18n('user.list-dept')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="userName"
          :label="transformI18n('user.list-username')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="title"
          :label="transformI18n('user.list-job-title')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="extension"
          :label="transformI18n('user.list-extension')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="email"
          :label="transformI18n('user.list-email')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="roleName"
          :label="transformI18n('user.list-rolename')"
          resizable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          fixed="right"
          width="200"
          :label="transformI18n('role.list-operations')"
          :show-overflow-tooltip="true"
          v-auth="['user:update', 'user:delete']"
        >
          <template v-slot="scope">
            <el-popover
              :visible="scope.row.alterRoleVisible"
              placement="left"
              :width="200"
              trigger="click"
            >
              <template #reference>
                <el-button
                  size="small"
                  :link="true"
                  :icon="useRenderIcon('edit')"
                  v-auth="['user:update']"
                  @click="handleAssignRole(scope.row)"
                  type="primary"
                  ><span>{{
                    transformI18n("user.list-assign-role")
                  }}</span></el-button
                >
              </template>
              <p>>{{ transformI18n("user.list-select-role") + ": " }}</p>
              <el-select v-model="selectedRole" filterable placeholder="Search">
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.roleName"
                  :value="item.id"
                />
              </el-select>
              <div style="text-align: center; margin-top: 15px">
                <el-button
                  size="small"
                  text
                  @click="scope.row.alterRoleVisible = false"
                  >{{ transformI18n("dialogue.cancel") }}</el-button
                >
                <el-button
                  size="small"
                  type="primary"
                  @click="onAssignRoleConfirm(scope.row)"
                  >{{ transformI18n("dialogue.confirm") }}</el-button
                >
              </div>
            </el-popover>

            <el-button
              size="small"
              :link="true"
              :icon="
                useRenderIcon(scope.row.isEnabled === '1' ? 'close' : 'user')
              "
              v-auth="['user:delete']"
              @click="handleDisableUser(scope.row)"
              :type="scope.row.isEnabled === '1' ? 'danger' : 'success'"
            >
              <span>{{
                scope.row.isEnabled === "1"
                  ? transformI18n("user.list-user-disable")
                  : transformI18n("user.list-user-enable")
              }}</span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="pageData.searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageData.searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.searchInfo.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </el-card>
  </div>
</template>

<style scoped>
.el-card {
  margin: 0px;
  padding: 0px;
  align-items: center;
  justify-content: center;
  width: 100% !important;
}

.query-card {
  margin-bottom: 20px;
  max-width: 1020px;
}

.el-pagination {
  margin-top: 10px;
}
</style>
