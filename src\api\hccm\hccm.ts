import BaseRequest from "../base";
enum API {
  BASE_URL = "/biz/hccm/",
  HCCM_POS = "distributeData",
  GROUP_LIST = "/group"
}

class HccmAPI extends BaseRequest {
  getBaseUrl(): string {
    return API.BASE_URL;
  }

  getHCCMData<T, Q>(query: Q): Promise<T> {
    return this.get<T>(API.HCCM_POS, query);
  }

  getGroupList<T>(): Promise<T> {
    return this.get<T>(API.GROUP_LIST);
  }
}

export const hccmAPIs = new HccmAPI();
