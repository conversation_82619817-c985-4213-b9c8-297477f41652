<script setup lang="ts">
import { useAcrStoreHook } from "/@/store/modules/acr";
defineOptions({
  name: "AcrInfo"
});
</script>

<template>
  <el-divider content-position="center">Position</el-divider>
  <el-row align="middle" style="width: 100%; height: 10%">
    <el-col align="middle" :span="12">
      <span>X(m): {{ useAcrStoreHook().getAcrInfo.xcoordinate }}</span>
    </el-col>
    <el-col align="middle" :span="12">
      <span>Y(m): {{ useAcrStoreHook().getAcrInfo.ycoordinate }}</span>
    </el-col>
  </el-row>
  <el-row align="middle" style="width: 100%; height: 8%">
    <el-col align="middle" :span="24">
      <span>Angle(°): {{ useAcrStoreHook().getAcrInfo.angle }}</span>
    </el-col>
  </el-row>
  <el-divider content-position="center">Battery</el-divider>
  <el-row align="middle" style="width: 100%; height: 10%">
    <el-col align="middle" :span="24">
      <span
        >Battery(%):
        <el-badge
          :value="useAcrStoreHook().getAcrInfo.battery"
          :type="
            useAcrStoreHook().getAcrInfo.battery > 15 ? 'success' : 'danger'
          "
        />
      </span>
    </el-col>
  </el-row>
  <el-divider content-position="center">Status</el-divider>
  <el-row align="middle" style="width: 100%; height: 10%">
    <el-col align="middle" :span="12">
      <span
        >Connect:
        <el-badge
          :value="
            useAcrStoreHook().getAcrInfo.connectStatus == 1 ? 'OK' : 'Lost'
          "
          :type="
            useAcrStoreHook().getAcrInfo.connectStatus == 1
              ? 'success'
              : 'danger'
          "
      /></span>
    </el-col>
    <el-col align="middle" :span="12">
      <span
        >Map:
        <el-badge
          :value="useAcrStoreHook().getAcrInfo.mapStatus == 1 ? 'OK' : 'NG'"
          :type="
            useAcrStoreHook().getAcrInfo.mapStatus == 1 ? 'success' : 'danger'
          "
      /></span>
    </el-col>
  </el-row>
  <el-row align="middle" style="width: 100%; height: 8%">
    <el-col align="middle" :span="12">
      <span
        >Error:
        <el-badge
          :value="
            useAcrStoreHook().getAcrInfo.abnormalFlag == 0 ? 'None' : 'NG'
          "
          :type="
            useAcrStoreHook().getAcrInfo.abnormalFlag == 0
              ? 'success'
              : 'danger'
          "
      /></span>
    </el-col>
    <el-col align="middle" :span="12">
      <span
        >Mode:
        <el-badge
          :value="
            useAcrStoreHook().getAcrInfo.controlMode == 1 ? 'AUTO' : 'MANUAL'
          "
          :type="
            useAcrStoreHook().getAcrInfo.controlMode == 1
              ? 'success'
              : 'primary'
          "
      /></span>
    </el-col>
  </el-row>
  <el-row align="middle" style="width: 100%; height: 8%">
    <el-col align="middle" :span="24">
      <span
        >Position Located:
        <el-badge
          :value="
            useAcrStoreHook().getAcrInfo.positionIsLocated == 1 ? 'OK' : 'NG'
          "
          :type="
            useAcrStoreHook().getAcrInfo.positionIsLocated == 1
              ? 'success'
              : 'danger'
          "
      /></span>
    </el-col>
  </el-row>
  <el-divider content-position="center">ZigBee</el-divider>
  <el-row align="middle" style="width: 100%; height: 10%">
    <el-col align="middle" :span="12">
      <span
        >Comm:
        <el-badge
          :value="
            useAcrStoreHook().getAcrInfo.onlineStatus == 1 ? 'OK' : 'Lost'
          "
          :type="
            useAcrStoreHook().getAcrInfo.onlineStatus == 1
              ? 'success'
              : 'danger'
          "
      /></span>
    </el-col>
    <el-col align="middle" :span="12">
      <span>XB: {{ useAcrStoreHook().getAcrInfo.xbeeName }}</span>
    </el-col>
  </el-row>
  <el-row align="middle" style="width: 100%; height: 8%">
    <el-col align="middle" :span="24">
      <span
        >Heartbeat: {{ useAcrStoreHook().getAcrInfo.lastHeartBeatTime }}</span
      >
    </el-col>
  </el-row>
  <el-row align="middle" style="width: 100%; height: 8%">
    <el-col align="middle" :span="24">
      <span
        >Last Update: {{ useAcrStoreHook().getAcrInfo.infoUpdatedTime }}</span
      >
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
.el-row {
  margin: 0;
  padding: 0;
}

.el-divider {
  padding: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.el-badge {
  margin-left: 0;
  margin-top: 0.5rem;
  padding: 0;
}

span {
  font-size: 0.8em;
}
</style>
