import { defineStore } from "pinia";
import { store } from "..";
export const acrStore = defineStore({
  id: "acr-store",
  state: () => ({
    factoryId: "4D",
    areaId: "4D-7C",
    mapMarkers: [],
    acrInfo: {
      acrId: ""
    },
    dispatcherStatus: {
      trayInfo: {
        _1: "",
        _2: "",
        _3: "",
        _4: "",
        _5: "",
        _6: "",
        _7: "",
        _8: ""
      }
    },
    lastOrder: []
  }),
  getters: {
    getFactoryId() {
      return this.factoryId;
    },
    getAreaId() {
      return this.areaId;
    },
    getMapMarkers() {
      return this.mapMarkers;
    },
    getAcrInfo() {
      return this.acrInfo;
    },
    getDispatcherStatus() {
      return this.dispatcherStatus;
    },
    getLastOrder() {
      return this.lastOrder;
    }
  },
  actions: {
    setFactoryId(factoryId) {
      this.factoryId = factoryId;
    },
    setAreaId(areaId) {
      this.areaId = areaId;
    },
    setMapMarkers(mapMarkers) {
      this.mapMarkers = mapMarkers;
    },
    setAcrInfo(acrInfo) {
      this.acrInfo = acrInfo;
    },
    setDispatcherStatus(dispatcherStatus) {
      this.dispatcherStatus = dispatcherStatus;
    },
    setLastOrder(lastOrder) {
      this.lastOrder = lastOrder;
    }
  }
});
export function useAcrStoreHook() {
  return acrStore(store);
}
